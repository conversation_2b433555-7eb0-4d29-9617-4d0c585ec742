
import React from 'react';

const stats = [
  { id: 1, value: '2,000+', label: 'Students' },
  { id: 2, value: '100+', label: 'Academic Staff' },
  { id: 3, value: '35+', label: 'Academic Programs' },
  { id: 4, value: '25+', label: 'Years of Excellence' },
  { id: 5, value: '4+', label: 'Research Centers' },
  { id: 6, value: '20,000+', label: 'Alumni Worldwide' },
  { id: 7, value: '6', label: 'Campuses' }
];

const QuickFacts = () => {
  return (
    <section className="py-16 bg-gradient-to-br from-aul-navy to-aul-blue text-white">
      <div className="aul-container">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">AUL at a Glance</h2>
          <div className="h-1 w-20 bg-aul-gold mx-auto rounded-full"></div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-7 gap-8">
          {stats.map((stat) => (
            <div key={stat.id} className="text-center group">
              <div className="text-4xl md:text-5xl font-bold text-aul-gold mb-2 group-hover:scale-110 transition-transform duration-300">{stat.value}</div>
              <div className="text-lg text-gray-200">{stat.label}</div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default QuickFacts;
