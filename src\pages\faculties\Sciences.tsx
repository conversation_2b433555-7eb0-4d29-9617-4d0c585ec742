import React from 'react';
import { Link } from 'react-router-dom';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowR<PERSON>, Microscope } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselPrevious,
  CarouselNext,
} from "@/components/ui/carousel";

const sciencePrograms = [
  {
    name: "Mathematics",
    description: "Study of numbers, quantities, shapes, and patterns through abstract concepts.",
    slug: "mathematics",
    images: [
      {
        src: "/images/sciences/mathematics.JPG",
        alt: "AUL Mathematics Department"
      },
      {
        src: "https://images.unsplash.com/photo-1635070041078-e363dbe005cb?auto=format&fit=crop&w=1200&q=80",
        alt: "Mathematics Formulas"
      },
      {
        src: "https://images.unsplash.com/photo-1509228468518-180dd4864904?auto=format&fit=crop&w=1200&q=80",
        alt: "Mathematics Study"
      }
    ]
  },
  {
    name: "Computer Science",
    description: "Study of algorithms, programming languages, and computational systems.",
    slug: "computer-science",
    images: [
      {
        src: "/images/sciences/computer science .JPG",
        alt: "AUL Computer Science Department"
      },
      {
        src: "/images/sciences/computer science 1.JPG",
        alt: "Computer Science Lab"
      },
      {
        src: "https://images.unsplash.com/photo-1518432031352-d6fc5c10da5a?auto=format&fit=crop&w=1200&q=80",
        alt: "Programming Environment"
      }
    ]
  },
  {
    name: "Computer Communication",
    description: "Study of computer networks, data communication, and network security.",
    slug: "computer-communication",
    images: [
      {
        src: "/images/sciences/computer communication.JPG",
        alt: "AUL Computer Communication Department"
      },
      {
        src: "/images/sciences/IMG_1647.JPG",
        alt: "Computer Communication Lab"
      },
      {
        src: "https://images.unsplash.com/photo-1558494949-ef010cbdcc31?auto=format&fit=crop&w=1200&q=80",
        alt: "Network Infrastructure"
      }
    ]
  },
  {
    name: "Graphic Design",
    description: "Study of visual communication and design principles.",
    slug: "graphic-design",
    images: [
      {
        src: "/images/sciences/Graphic design.JPG",
        alt: "AUL Graphic Design Department"
      },
      {
        src: "https://images.unsplash.com/photo-1626785774625-ddcddc3445e9?auto=format&fit=crop&w=1200&q=80",
        alt: "Digital Design Work"
      },
      {
        src: "https://images.unsplash.com/photo-1626785774573-4b799315345d?auto=format&fit=crop&w=1200&q=80",
        alt: "Design Process"
      }
    ]
  },
  {
    name: "Interior Design",
    description: "Study of interior spaces, design principles, and spatial planning.",
    slug: "interior-design",
    images: [
      {
        src: "/images/sciences/Interior design.JPG",
        alt: "AUL Interior Design Department"
      },
      {
        src: "https://images.unsplash.com/photo-1616486338812-3dadae4b4ace?auto=format&fit=crop&w=1200&q=80",
        alt: "Modern Interior"
      },
      {
        src: "https://images.unsplash.com/photo-1616486029423-aaa4789e8c9a?auto=format&fit=crop&w=1200&q=80",
        alt: "Design Project"
      }
    ]
  },
  {
    name: "Master of Science in Computer Science and Communication",
    description: "Advanced study in computer science and communication technologies.",
    slug: "msc-computer-science-communication",
    images: [
      {
        src: "/images/sciences/master of science in computer science.JPG",
        alt: "AUL Master's Program"
      },
      {
        src: "/images/sciences/IMG_4008.JPG",
        alt: "Graduate Research Lab"
      },
      {
        src: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?auto=format&fit=crop&w=1200&q=80",
        alt: "Research Presentation"
      }
    ]
  }
];

const facilitiesImages = [
  {
    src: '/images/sciences/computer science .JPG',
    alt: 'Computer Science Department'
  },
  {
    src: '/images/sciences/mathematics.JPG',
    alt: 'Mathematics Department'
  },
  {
    src: '/images/sciences/computer communication.JPG',
    alt: 'Computer Communication Department'
  },
  {
    src: '/images/sciences/Graphic design.JPG',
    alt: 'Graphic Design Department'
  },
  {
    src: '/images/sciences/master of science in computer science.JPG',
    alt: 'Master of Science Program'
  },
  {
    src: '/images/sciences/computer science 1.JPG',
    alt: 'Computer Science Lab'
  },
  {
    src: '/images/sciences/IMG_1647.JPG',
    alt: 'Communication Lab'
  },
  {
    src: '/images/sciences/IMG_4008.JPG',
    alt: 'Research Facilities'
  }
];

const Sciences = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow py-12">
        <div className="container mx-auto px-4">
          <div className="flex items-center gap-4 mb-8">
            <Microscope className="w-12 h-12 text-aul-gold" />
            <h1 className="text-4xl font-bold text-aul-navy">Faculty of Sciences</h1>
          </div>

          <section className="mb-12">
            <Card className="overflow-hidden">
              <Carousel className="w-full" opts={{ loop: true, duration: 20 }}>
                <CarouselContent>
                  {facilitiesImages.map((image, index) => (
                    <CarouselItem key={index}>
                      <div className="relative aspect-[21/9]">
                        <img
                          src={image.src}
                          alt={image.alt}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </CarouselItem>
                  ))}
                </CarouselContent>
                <CarouselPrevious className="left-2 z-20 bg-white text-black hover:bg-gray-200" />
                <CarouselNext className="right-2 z-20 bg-white text-black hover:bg-gray-200" />
              </Carousel>

              <CardContent className="py-6">
                <h2 className="text-2xl font-bold text-aul-navy mb-4">About the Faculty</h2>
                <p className="text-gray-700 mb-4">
                  The Faculty of Sciences at AUL is dedicated to advancing scientific knowledge through innovative research and exceptional education. Our programs combine theoretical knowledge with practical applications, preparing students to tackle complex scientific challenges.
                </p>
                <p className="text-gray-700">
                  With cutting-edge laboratories, research facilities, and experienced faculty members, we provide our students with the tools and guidance they need to explore the frontiers of science and make significant contributions to their fields.
                </p>
              </CardContent>
            </Card>
          </section>

          <section>
            <h2 className="text-2xl font-bold text-aul-navy mb-6">Our Programs</h2>
            <div className="grid md:grid-cols-2 gap-8">
              {sciencePrograms.map((program, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow overflow-hidden">
                  <Carousel className="w-full" opts={{ loop: true, duration: 20 }}>
                    <CarouselContent>
                      {program.images.map((image, imageIndex) => (
                        <CarouselItem key={imageIndex}>
                          <div className="relative aspect-video">
                            <img
                              src={image.src}
                              alt={image.alt}
                              className="w-full h-full object-cover"
                            />
                            <div className="absolute inset-0 bg-black/20"></div>
                          </div>
                        </CarouselItem>
                      ))}
                    </CarouselContent>
                    <CarouselPrevious className="left-2 z-20 bg-white text-black hover:bg-gray-200" />
                    <CarouselNext className="right-2 z-20 bg-white text-black hover:bg-gray-200" />
                  </Carousel>

                  <CardHeader>
                    <CardTitle>{program.name}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 mb-4">{program.description}</p>
                    <Button asChild variant="outline" size="sm" className="mt-2">
                      <Link to={`/programs/${program.slug}`} className="flex items-center">
                        Explore Program <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>

          <section className="mt-12">
            <h2 className="text-2xl font-bold text-aul-navy mb-6">Research Areas</h2>
            <ul className="grid md:grid-cols-3 gap-4 list-disc list-inside text-gray-700">
              <li>Artificial Intelligence</li>
              <li>Machine Learning</li>
              <li>Molecular Biology</li>
              <li>Genetics</li>
              <li>Number Theory</li>
              <li>Applied Mathematics</li>
              <li>Organic Chemistry</li>
              <li>Biochemistry</li>
              <li>Material Sciences</li>
            </ul>
          </section>

          <section className="mt-12">
            <Card>
              <CardContent className="p-6">
                <h2 className="text-2xl font-bold text-aul-navy mb-4">Contact the Faculty</h2>
                <p className="text-gray-700 mb-2">Faculty Dean: Dr. Sarah Johnson</p>
                <p className="text-gray-700 mb-2">Email: <EMAIL></p>
                <p className="text-gray-700 mb-2">Phone: (*************</p>
                <p className="text-gray-700">Location: Science Complex, East Campus</p>
              </CardContent>
            </Card>
          </section>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Sciences;
