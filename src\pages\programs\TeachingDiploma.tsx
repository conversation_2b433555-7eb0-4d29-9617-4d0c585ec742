import React from 'react';
import ProgramDetail from '@/components/programs/ProgramDetail';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

const TeachingDiploma = () => {
  const programData = {
    name: "Teaching Diploma",
    description: "The Teaching Diploma program at AUL is a professional program preparing students for careers in education. The curriculum combines educational theory, teaching methodologies, and practical classroom experience to develop effective educators who can inspire and guide the next generation of learners.",
    duration: "1 year (2 semesters)",
    faculty: "Faculty of Arts & Humanities",
    degreeAwarded: "Teaching Diploma",
    majorHighlights: [
      "Comprehensive teaching methodologies",
      "Classroom management techniques",
      "Educational psychology and child development",
      "Practical teaching experience",
      "Curriculum design and assessment",
      "Educational technology integration",
      "Student-centered learning approaches",
      "Professional development opportunities"
    ],
    careerOpportunities: [
      "School Teacher",
      "Educational Administrator",
      "Curriculum Developer",
      "Educational Consultant",
      "Special Education Teacher",
      "Corporate Trainer",
      "Educational Technology Specialist",
      "Academic Advisor",
      "Private Tutor",
      "Educational Program Coordinator",
      "Instructional Designer",
      "Educational Materials Developer",
      "Language Teacher",
      "Adult Education Instructor",
      "Educational NGO Worker"
    ],
    courseHighlights: [
      {
        year: "First Semester",
        courses: [
          "EDUC 501: Educational Psychology (3 credits)",
          "EDUC 503: Teaching Methods (3 credits)",
          "EDUC 505: Classroom Management (3 credits)",
          "EDUC 507: Curriculum Development (3 credits)",
          "EDUC 509: Educational Technology (3 credits)"
        ]
      },
      {
        year: "Second Semester",
        courses: [
          "EDUC 502: Assessment and Evaluation (3 credits)",
          "EDUC 504: Special Education (3 credits)",
          "EDUC 506: Educational Leadership (3 credits)",
          "EDUC 508: Professional Ethics in Education (3 credits)",
          "EDUC 510: Teaching Practicum (6 credits)"
        ]
      }
    ],
    admissionRequirements: [
      "Bachelor's degree in any field",
      "Minimum GPA of 2.5 on a 4.0 scale or equivalent",
      "English language proficiency (TOEFL score of 550+ or equivalent)",
      "Personal interview with the department",
      "Statement of teaching philosophy",
      "Two letters of recommendation"
    ],
    programObjectives: [
      "Develop effective teaching strategies and methodologies",
      "Build classroom management and leadership skills",
      "Understand educational psychology and child development",
      "Master curriculum design and assessment techniques",
      "Integrate educational technology in teaching practices",
      "Prepare for successful careers in education",
      "Foster ethical awareness and professional responsibility",
      "Develop reflective teaching practices"
    ],
    videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ"
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow">
        <ProgramDetail program={programData} />
      </main>
      <Footer />
    </div>
  );
};

export default TeachingDiploma;
