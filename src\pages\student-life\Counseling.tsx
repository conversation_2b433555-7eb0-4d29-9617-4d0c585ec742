
import React from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Headphones } from 'lucide-react';

const Counseling = () => {
  const services = [
    {
      title: "Academic Counseling",
      description: "Get support with course selection, study strategies, and academic planning."
    },
    {
      title: "Mental Health Support",
      description: "Professional counseling for stress, anxiety, depression, and other mental health concerns."
    },
    {
      title: "Career Guidance",
      description: "Career planning, resume writing, and interview preparation assistance."
    },
    {
      title: "Crisis Support",
      description: "24/7 emergency support services for students in crisis."
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow py-12">
        <div className="container mx-auto px-4">
          <div className="flex items-center gap-4 mb-8">
            <Headphones className="w-12 h-12 text-aul-gold" />
            <h1 className="text-4xl font-bold text-aul-navy">Counseling Services</h1>
          </div>

          <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-8">
            <p className="font-bold">24/7 Crisis Hotline: +961 1 234 567</p>
            <p className="text-gray-600">If you're experiencing a mental health emergency, please don't hesitate to call.</p>
          </div>
          
          <p className="text-lg text-gray-600 mb-8">
            Our counseling center provides confidential support services to help students 
            maintain their mental health and achieve their academic goals.
          </p>

          <div className="grid md:grid-cols-2 gap-8 mb-12">
            {services.map((service, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle>{service.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">{service.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center">
            <Button size="lg" className="bg-aul-navy hover:bg-aul-gold">
              Schedule an Appointment
            </Button>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Counseling;
