# AUL AI Teaching Assistant - Environment Configuration
# Copy this file to .env and fill in your actual values

# ================================
# FIREBASE CONFIGURATION
# ================================
# Get these from your Firebase project settings
VITE_FIREBASE_API_KEY=your_firebase_api_key_here
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=123456789
VITE_FIREBASE_APP_ID=your_app_id_here
VITE_FIREBASE_MEASUREMENT_ID=your_measurement_id_here

# ================================
# GOOGLE GEMINI API
# ================================
# Get your API key from https://makersuite.google.com/app/apikey
VITE_GEMINI_API_KEY=your_gemini_api_key_here

# ================================
# SERVER CONFIGURATION
# ================================
PORT=5000
NODE_ENV=development
CORS_ORIGIN=http://localhost:3000

# ================================
# APPLICATION SETTINGS
# ================================
VITE_APP_NAME=AUL AI Teaching Assistant
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=AI-powered teaching assistant for AUL

# ================================
# FEATURE FLAGS
# ================================
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_ERROR_REPORTING=false
VITE_ENABLE_ADMIN_PANEL=true
VITE_ENABLE_COURSE_UPLOAD=true

# ================================
# AI CONFIGURATION
# ================================
VITE_MAX_MESSAGE_LENGTH=2000
VITE_MAX_CONVERSATION_HISTORY=50
VITE_AI_RESPONSE_TIMEOUT=30000
VITE_MAX_SOURCES_PER_RESPONSE=5

# ================================
# UPLOAD LIMITS
# ================================
VITE_MAX_FILE_SIZE=50000000
VITE_ALLOWED_FILE_TYPES=pdf,docx,txt,md
VITE_MAX_FILES_PER_COURSE=100

# ================================
# RATE LIMITING
# ================================
VITE_RATE_LIMIT_REQUESTS=100
VITE_RATE_LIMIT_WINDOW=900000

# ================================
# DEVELOPMENT SETTINGS
# ================================
VITE_DEBUG_MODE=true
VITE_MOCK_AI_RESPONSES=false
VITE_ENABLE_CONSOLE_LOGS=true
