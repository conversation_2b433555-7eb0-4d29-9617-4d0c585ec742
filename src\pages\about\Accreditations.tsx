
import React from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Award, FileText, Target, Users, BookOpen, Building, TrendingUp, Download, FolderOpen } from 'lucide-react';

const Accreditations = () => {
  const accreditations = [
    {
      organization: "Higher Education Council",
      status: "Full Institutional Accreditation",
      year: "2024",
      description: "Recognized for excellence in academic standards and institutional quality."
    },
    {
      organization: "Engineering Accreditation Board",
      status: "Program-Specific Accreditation",
      year: "2023",
      description: "All engineering programs meet international standards for professional engineering education."
    },
    {
      organization: "Business Education Alliance",
      status: "Premium Accreditation",
      year: "2023",
      description: "Distinguished recognition for business and management programs."
    },
    {
      organization: "International Quality Assurance Agency",
      status: "Quality Excellence Certificate",
      year: "2024",
      description: "Meets global standards for educational quality and student outcomes."
    }
  ];

  // Self-Assessment Summary - Main Points from the Document
  const selfAssessmentSummary = {
    overview: {
      title: "Institutional Self-Assessment Overview",
      description: "AUL conducts comprehensive self-assessment to ensure continuous improvement and maintain the highest standards of educational excellence.",
      lastUpdated: "2024"
    },
    keyAreas: [
      {
        icon: <Target className="w-6 h-6" />,
        title: "Mission & Strategic Planning",
        description: "Clear institutional mission aligned with strategic objectives and measurable outcomes.",
        highlights: [
          "Well-defined mission statement and institutional goals",
          "Strategic planning process with stakeholder involvement",
          "Regular review and assessment of strategic objectives",
          "Alignment between mission and academic programs"
        ]
      },
      {
        icon: <BookOpen className="w-6 h-6" />,
        title: "Academic Programs & Curriculum",
        description: "Comprehensive academic offerings designed to meet student needs and industry requirements.",
        highlights: [
          "Diverse range of undergraduate and graduate programs",
          "Regular curriculum review and updates",
          "Industry-relevant course content and learning outcomes",
          "Integration of practical and theoretical components"
        ]
      },
      {
        icon: <Users className="w-6 h-6" />,
        title: "Faculty & Staff Excellence",
        description: "Qualified faculty and staff committed to educational excellence and student success.",
        highlights: [
          "Highly qualified faculty with advanced degrees",
          "Ongoing professional development programs",
          "Research and scholarly activities",
          "Student-centered teaching approaches"
        ]
      },
      {
        icon: <Building className="w-6 h-6" />,
        title: "Infrastructure & Resources",
        description: "Modern facilities and resources supporting effective teaching, learning, and research.",
        highlights: [
          "State-of-the-art laboratories and classrooms",
          "Comprehensive library and digital resources",
          "Technology-enhanced learning environments",
          "Accessible and safe campus facilities"
        ]
      },
      {
        icon: <TrendingUp className="w-6 h-6" />,
        title: "Student Success & Outcomes",
        description: "Strong focus on student achievement, satisfaction, and post-graduation success.",
        highlights: [
          "High student satisfaction rates",
          "Strong graduate employment outcomes",
          "Comprehensive student support services",
          "Active alumni engagement and feedback"
        ]
      },
      {
        icon: <CheckCircle className="w-6 h-6" />,
        title: "Quality Assurance & Improvement",
        description: "Systematic approach to quality assurance and continuous institutional improvement.",
        highlights: [
          "Regular internal and external quality reviews",
          "Data-driven decision making processes",
          "Stakeholder feedback integration",
          "Continuous improvement culture"
        ]
      }
    ],
    strengths: [
      "Strong institutional governance and leadership",
      "Comprehensive academic program portfolio",
      "Qualified and dedicated faculty",
      "Modern facilities and technology infrastructure",
      "Active industry partnerships and collaborations",
      "Diverse and inclusive campus community",
      "Strong financial stability and resource management"
    ],
    improvementAreas: [
      "Enhanced research and innovation activities",
      "Expanded international partnerships",
      "Increased digital learning capabilities",
      "Strengthened alumni engagement programs",
      "Enhanced student career services",
      "Improved sustainability initiatives"
    ]
  };

  // Policies and Procedures Documents
  const policiesAndProcedures = {
    academic: [
      {
        title: "Academic Petition",
        filename: "Academic Petition Acad.P07.pdf",
        description: "Guidelines for academic petition processes and requirements"
      },
      {
        title: "Academic Recruitment",
        filename: "Academic recruitment Acad. P02.pdf",
        description: "Procedures for academic staff recruitment and selection"
      },
      {
        title: "Course Advising",
        filename: "Course Advising Acad. P05.pdf",
        description: "Academic advising procedures and student guidance protocols"
      },
      {
        title: "Course Offering",
        filename: "Course offering Acad. P06.pdf",
        description: "Guidelines for course planning and offering procedures"
      },
      {
        title: "Course Substitution",
        filename: "Course substitution Acad. P04.pdf",
        description: "Procedures for course substitution and equivalency"
      },
      {
        title: "Graduation Requirements",
        filename: "Graduation  Acad. P03 .pdf",
        description: "Comprehensive graduation requirements and procedures"
      },
      {
        title: "Teaching Methodology",
        filename: "Teaching Methodology Acad. P08.pdf",
        description: "Standards and guidelines for effective teaching methodologies"
      },
      {
        title: "Transfer Credits",
        filename: "Transfer Credits Acad.  P01.pdf",
        description: "Policies for transfer credit evaluation and acceptance"
      }
    ],
    examinations: [
      {
        title: "Common Exam Procedures",
        filename: "Common Exam OTA-P07.pdf",
        description: "Guidelines for conducting common examinations"
      },
      {
        title: "Exam Scheduling",
        filename: "Scheduling of Exams OTA-P01.pdf",
        description: "Procedures for examination scheduling and coordination"
      },
      {
        title: "Exam Management",
        filename: "Management of Exams OTA-P04.pdf",
        description: "Comprehensive exam management and administration guidelines"
      },
      {
        title: "Late Students Policy",
        filename: "Late Students for Exams OTA-P05.pdf",
        description: "Policies regarding late arrival to examinations"
      },
      {
        title: "Makeup Exam Manual",
        filename: "Makeup exam manual OTA-M02.pdf",
        description: "Procedures for makeup examinations and special circumstances"
      }
    ],
    grading: [
      {
        title: "Change of Grade",
        filename: "Change of Grade R-P04.pdf",
        description: "Procedures for grade changes and corrections"
      },
      {
        title: "Grade Recheck",
        filename: "Re check grade R-P01.pdf",
        description: "Guidelines for grade review and recheck processes"
      },
      {
        title: "Instructor Error Correction",
        filename: "Instructor_s Error R-P03.pdf",
        description: "Procedures for correcting instructor errors in grading"
      }
    ],
    integrity: [
      {
        title: "Academic Integrity Policy",
        filename: "Cheating  OTA-P02 .pdf",
        description: "Comprehensive policy on academic integrity and cheating prevention"
      }
    ],
    assessment: [
      {
        title: "Program Assessment",
        filename: "Program assessment.pdf",
        description: "Guidelines for academic program assessment and evaluation"
      },
      {
        title: "Course File Manual",
        filename: "Course File Manual.pdf",
        description: "Standards for course documentation and file management"
      }
    ]
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow py-12">
        <div className="container mx-auto px-4">
          <div className="flex items-center gap-4 mb-8">
            <Award className="w-12 h-12 text-aul-gold" />
            <h1 className="text-4xl font-bold text-aul-navy">Accreditations & Quality Assurance</h1>
          </div>

          <p className="text-lg text-gray-600 mb-12">
            AUL University maintains the highest standards of educational excellence, validated through
            accreditation by leading national and international organizations and comprehensive self-assessment processes.
          </p>

          {/* Current Accreditations Section */}
          <section className="mb-16">
            <h2 className="text-3xl font-bold text-aul-navy mb-8">Current Accreditations</h2>
            <div className="grid md:grid-cols-2 gap-8">
              {accreditations.map((item, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <CheckCircle className="w-5 h-5 text-green-600" />
                      {item.organization}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <span className="font-semibold">Status: </span>
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          {item.status}
                        </Badge>
                      </div>
                      <div>
                        <span className="font-semibold">Year: </span>
                        <span className="text-aul-navy font-medium">{item.year}</span>
                      </div>
                      <p className="text-gray-600">{item.description}</p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>

          {/* Self-Assessment Section */}
          <section className="mb-16">
            <div className="flex items-center gap-3 mb-8">
              <FileText className="w-8 h-8 text-aul-gold" />
              <div>
                <h2 className="text-3xl font-bold text-aul-navy">{selfAssessmentSummary.overview.title}</h2>
                <p className="text-gray-600 mt-2">{selfAssessmentSummary.overview.description}</p>
              </div>
            </div>

            {/* Key Assessment Areas */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
              {selfAssessmentSummary.keyAreas.map((area, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-3 text-lg">
                      <div className="text-aul-gold">{area.icon}</div>
                      {area.title}
                    </CardTitle>
                    <CardDescription>{area.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {area.highlights.map((highlight, idx) => (
                        <li key={idx} className="flex items-start gap-2 text-sm">
                          <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700">{highlight}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Strengths and Improvement Areas */}
            <div className="grid md:grid-cols-2 gap-8">
              <Card className="border-green-200 bg-green-50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-green-800">
                    <TrendingUp className="w-6 h-6" />
                    Institutional Strengths
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {selfAssessmentSummary.strengths.map((strength, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700">{strength}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              <Card className="border-blue-200 bg-blue-50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-blue-800">
                    <Target className="w-6 h-6" />
                    Areas for Continuous Improvement
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {selfAssessmentSummary.improvementAreas.map((area, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <Target className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700">{area}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </div>
          </section>

          {/* Policies and Procedures Section */}
          <section className="mb-16">
            <div className="flex items-center gap-3 mb-8">
              <FolderOpen className="w-8 h-8 text-aul-gold" />
              <div>
                <h2 className="text-3xl font-bold text-aul-navy">Policies and Procedures</h2>
                <p className="text-gray-600 mt-2">
                  Comprehensive documentation of institutional policies and procedures ensuring quality and consistency across all academic operations.
                </p>
              </div>
            </div>

            <div className="space-y-8">
              {/* Academic Policies */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BookOpen className="w-6 h-6 text-aul-gold" />
                    Academic Policies
                  </CardTitle>
                  <CardDescription>
                    Core academic policies governing student progression, course management, and academic standards.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {policiesAndProcedures.academic.map((policy, index) => (
                      <div key={index} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div className="flex items-start justify-between mb-2">
                          <h4 className="font-semibold text-aul-navy text-sm">{policy.title}</h4>
                          <a
                            href={`/documents/policies/${policy.filename}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-aul-gold hover:text-aul-navy transition-colors"
                          >
                            <Download className="w-4 h-4" />
                          </a>
                        </div>
                        <p className="text-gray-600 text-xs mb-3">{policy.description}</p>
                        <a
                          href={`/documents/policies/${policy.filename}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-xs text-aul-blue hover:text-aul-navy font-medium"
                        >
                          View Document →
                        </a>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Examination Policies */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="w-6 h-6 text-aul-gold" />
                    Examination Policies
                  </CardTitle>
                  <CardDescription>
                    Comprehensive guidelines for examination administration, scheduling, and management.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {policiesAndProcedures.examinations.map((policy, index) => (
                      <div key={index} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div className="flex items-start justify-between mb-2">
                          <h4 className="font-semibold text-aul-navy text-sm">{policy.title}</h4>
                          <a
                            href={`/documents/policies/${policy.filename}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-aul-gold hover:text-aul-navy transition-colors"
                          >
                            <Download className="w-4 h-4" />
                          </a>
                        </div>
                        <p className="text-gray-600 text-xs mb-3">{policy.description}</p>
                        <a
                          href={`/documents/policies/${policy.filename}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-xs text-aul-blue hover:text-aul-navy font-medium"
                        >
                          View Document →
                        </a>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Grading Policies */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="w-6 h-6 text-aul-gold" />
                    Grading Policies
                  </CardTitle>
                  <CardDescription>
                    Policies and procedures for grade management, corrections, and review processes.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {policiesAndProcedures.grading.map((policy, index) => (
                      <div key={index} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div className="flex items-start justify-between mb-2">
                          <h4 className="font-semibold text-aul-navy text-sm">{policy.title}</h4>
                          <a
                            href={`/documents/policies/${policy.filename}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-aul-gold hover:text-aul-navy transition-colors"
                          >
                            <Download className="w-4 h-4" />
                          </a>
                        </div>
                        <p className="text-gray-600 text-xs mb-3">{policy.description}</p>
                        <a
                          href={`/documents/policies/${policy.filename}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-xs text-aul-blue hover:text-aul-navy font-medium"
                        >
                          View Document →
                        </a>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Academic Integrity & Assessment */}
              <div className="grid md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <CheckCircle className="w-6 h-6 text-aul-gold" />
                      Academic Integrity
                    </CardTitle>
                    <CardDescription>
                      Policies ensuring academic honesty and integrity standards.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {policiesAndProcedures.integrity.map((policy, index) => (
                        <div key={index} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                          <div className="flex items-start justify-between mb-2">
                            <h4 className="font-semibold text-aul-navy text-sm">{policy.title}</h4>
                            <a
                              href={`/documents/policies/${policy.filename}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-aul-gold hover:text-aul-navy transition-colors"
                            >
                              <Download className="w-4 h-4" />
                            </a>
                          </div>
                          <p className="text-gray-600 text-xs mb-3">{policy.description}</p>
                          <a
                            href={`/documents/policies/${policy.filename}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-xs text-aul-blue hover:text-aul-navy font-medium"
                          >
                            View Document →
                          </a>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Target className="w-6 h-6 text-aul-gold" />
                      Assessment & Documentation
                    </CardTitle>
                    <CardDescription>
                      Guidelines for program assessment and course documentation standards.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {policiesAndProcedures.assessment.map((policy, index) => (
                        <div key={index} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                          <div className="flex items-start justify-between mb-2">
                            <h4 className="font-semibold text-aul-navy text-sm">{policy.title}</h4>
                            <a
                              href={`/documents/policies/${policy.filename}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-aul-gold hover:text-aul-navy transition-colors"
                            >
                              <Download className="w-4 h-4" />
                            </a>
                          </div>
                          <p className="text-gray-600 text-xs mb-3">{policy.description}</p>
                          <a
                            href={`/documents/policies/${policy.filename}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-xs text-aul-blue hover:text-aul-navy font-medium"
                          >
                            View Document →
                          </a>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </section>

          {/* Quality Commitment Statement */}
          <section>
            <Card className="bg-gradient-to-r from-aul-navy to-aul-blue text-white">
              <CardContent className="p-8">
                <div className="text-center">
                  <Award className="w-16 h-16 text-aul-gold mx-auto mb-4" />
                  <h3 className="text-2xl font-bold mb-4">Our Commitment to Excellence</h3>
                  <p className="text-lg leading-relaxed max-w-4xl mx-auto">
                    AUL is committed to maintaining the highest standards of educational quality through
                    continuous self-assessment, stakeholder engagement, and evidence-based improvement.
                    Our accreditations and quality assurance processes ensure that we deliver exceptional
                    educational experiences that prepare our students for success in their chosen careers
                    and contribute meaningfully to society.
                  </p>
                  <div className="mt-6">
                    <Badge variant="secondary" className="bg-aul-gold text-aul-navy font-semibold px-4 py-2">
                      Last Updated: {selfAssessmentSummary.overview.lastUpdated}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </section>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Accreditations;
