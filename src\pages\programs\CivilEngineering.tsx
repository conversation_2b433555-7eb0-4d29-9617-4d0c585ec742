
import React from 'react';
import ProgramDetail from '@/components/programs/ProgramDetail';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

const CivilEngineering = () => {
  const programData = {
    name: "Bachelor of Engineering in Civil Engineering",
    description: "The Civil Engineering program at AUL focuses on the design, construction, and maintenance of the built environment. Our curriculum provides a strong foundation in engineering principles while emphasizing sustainable and innovative approaches to infrastructure development. Students learn to solve complex engineering challenges through hands-on projects, laboratory work, and field experiences, preparing them for successful careers in this essential engineering discipline.",
    duration: "4 years (8 semesters)",
    faculty: "Faculty of Engineering",
    degreeAwarded: "Bachelor of Engineering (BEng) in Civil Engineering",
    majorHighlights: [
      "State-of-the-art structural and materials testing laboratories",
      "Advanced computer-aided design and simulation facilities",
      "Field trips to major construction and infrastructure projects",
      "Industry partnerships providing internship opportunities",
      "Experienced faculty with extensive professional experience",
      "Capstone design projects addressing real-world engineering challenges",
      "Focus on sustainable and resilient infrastructure design",
      "Preparation for professional engineering certification"
    ],
    careerOpportunities: [
      "Structural Engineer",
      "Construction Manager",
      "Transportation Engineer",
      "Environmental Engineer",
      "Geotechnical Engineer",
      "Water Resources Engineer",
      "Urban Planner",
      "Project Manager",
      "Building Control Surveyor",
      "Consulting Civil Engineer",
      "Site Engineer",
      "Infrastructure Engineer",
      "Hydraulic Engineer",
      "Municipal Engineer",
      "Coastal Engineer"
    ],
    courseHighlights: [
      {
        year: "First Year - Semester 1",
        courses: [
          "CIVE 201: Introduction to Civil Engineering (3 credits)",
          "ENGR 201: Engineering Mechanics I - Statics (3 credits)",
          "MATH 211: Calculus I (3 credits)",
          "PHYS 201: Physics for Engineers I (3 credits)",
          "ENGL 201: English Communication Skills I (3 credits)"
        ]
      },
      {
        year: "First Year - Semester 2",
        courses: [
          "ENGR 202: Engineering Mechanics II - Dynamics (3 credits)",
          "CIVE 202: Engineering Drawing & CAD (3 credits)",
          "MATH 212: Calculus II (3 credits)",
          "PHYS 202: Physics for Engineers II (3 credits)",
          "CHEM 201: Chemistry for Engineers (3 credits)"
        ]
      },
      {
        year: "Second Year - Semester 3",
        courses: [
          "CIVE 301: Strength of Materials (3 credits)",
          "CIVE 303: Surveying (3 credits)",
          "CIVE 305: Construction Materials (3 credits)",
          "MATH 311: Differential Equations (3 credits)",
          "ENGL 301: Technical Writing (2 credits)"
        ]
      },
      {
        year: "Second Year - Semester 4",
        courses: [
          "CIVE 302: Structural Analysis I (3 credits)",
          "CIVE 304: Fluid Mechanics (3 credits)",
          "CIVE 306: Soil Mechanics (3 credits)",
          "MATH 312: Numerical Methods (3 credits)",
          "COMP 301: Computer Programming for Engineers (3 credits)"
        ]
      },
      {
        year: "Third Year - Semester 5",
        courses: [
          "CIVE 401: Reinforced Concrete Design I (3 credits)",
          "CIVE 403: Highway Engineering (3 credits)",
          "CIVE 405: Hydraulic Engineering (3 credits)",
          "CIVE 407: Geotechnical Engineering (3 credits)",
          "ENGR 401: Engineering Economics (3 credits)"
        ]
      },
      {
        year: "Third Year - Semester 6",
        courses: [
          "CIVE 402: Steel Structure Design (3 credits)",
          "CIVE 404: Transportation Engineering (3 credits)",
          "CIVE 406: Environmental Engineering (3 credits)",
          "CIVE 408: Construction Management (3 credits)",
          "CIVE 410: Summer Training (0 credits)"
        ]
      },
      {
        year: "Fourth Year - Semester 7",
        courses: [
          "CIVE 501: Reinforced Concrete Design II (3 credits)",
          "CIVE 503: Foundation Engineering (3 credits)",
          "CIVE 505: Water Supply & Wastewater Systems (3 credits)",
          "CIVE 507: Engineering Ethics & Professionalism (2 credits)",
          "CIVE 509: Capstone Design Project I (2 credits)"
        ]
      },
      {
        year: "Fourth Year - Semester 8",
        courses: [
          "CIVE 502: Structural Analysis II (3 credits)",
          "CIVE 504: Construction Planning & Scheduling (3 credits)",
          "CIVE 506: Engineering Contracts & Specifications (2 credits)",
          "CIVE 508: Elective Course (3 credits)",
          "CIVE 510: Capstone Design Project II (3 credits)"
        ]
      }
    ],
    admissionRequirements: [
      "Lebanese Baccalaureate (Scientific Stream) or equivalent",
      "Minimum overall average of 12/20 or equivalent",
      "Strong background in Mathematics and Physics",
      "Spatial reasoning skills",
      "English language proficiency (TOEFL score of 500+ or equivalent)",
      "Personal interview with the department"
    ],
    programObjectives: [
      "Develop strong problem-solving and analytical skills for engineering challenges",
      "Master fundamental principles of civil engineering design and analysis",
      "Apply engineering knowledge to create sustainable and resilient infrastructure",
      "Use modern engineering tools, software, and technologies effectively",
      "Understand professional and ethical responsibilities of civil engineers",
      "Develop effective communication and teamwork skills",
      "Prepare for successful careers in design, construction, and infrastructure management",
      "Foster innovation and entrepreneurial thinking in civil engineering practice"
    ],
    videoUrl: "https://www.youtube.com/embed/cJrkkLhlr-E"
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow">
        <ProgramDetail program={programData} />
      </main>
      <Footer />
    </div>
  );
};

export default CivilEngineering;
