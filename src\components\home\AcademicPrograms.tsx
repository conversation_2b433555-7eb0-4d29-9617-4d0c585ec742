
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowRight } from 'lucide-react';

// Mock data
const faculties = [
  {
    id: 1,
    name: "Faculty of Sciences",
    description: "Offering programs in Computer Science, Mathematics, Biology, and Chemistry.",
    image: "/images/home/<USER>",
    url: "/faculties/sciences",
    programs: ["Computer Science", "Biology", "Mathematics", "Chemistry"]
  },
  {
    id: 2,
    name: "Faculty of Engineering",
    description: "Providing innovative programs in Civil, Electrical, Mechanical, and Computer Engineering.",
    image: "/images/home/<USER>",
    url: "/faculties/engineering",
    programs: ["Civil Engineering", "Electrical Engineering", "Mechanical Engineering", "Computer Engineering"]
  },
  {
    id: 3,
    name: "Faculty of Business",
    description: "Developing future business leaders through programs in Management, Finance, Marketing, and Accounting.",
    image: "/images/home/<USER>",
    url: "/faculties/business",
    programs: ["Business Administration", "Finance", "Marketing", "Accounting"]
  },
  {
    id: 4,
    name: "Faculty of Arts",
    description: "Cultivating creativity and critical thinking through programs in Design, Media, Languages, and Social Sciences.",
    image: "/images/home/<USER>",
    url: "/faculties/arts",
    programs: ["Graphic Design", "Digital Media", "English Literature", "Psychology"]
  }
];

const AcademicPrograms = () => {
  return (
    <section className="section-padding bg-white">
      <div className="aul-container">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-aul-navy mb-4">Academic Programs</h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            AUL offers a wide range of undergraduate and graduate programs across various disciplines,
            designed to prepare students for successful careers and meaningful contributions to society.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {faculties.map((faculty) => (
            <Card key={faculty.id} className="card-hover h-full flex flex-col overflow-hidden">
              <div className="relative h-48 overflow-hidden">
                <img
                  src={faculty.image}
                  alt={faculty.name}
                  className="w-full h-full object-cover transition-transform duration-300 hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
              </div>
              <CardHeader>
                <CardTitle className="text-xl">{faculty.name}</CardTitle>
                <CardDescription className="text-base">
                  {faculty.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="flex-grow">
                <h4 className="font-semibold mb-2 text-aul-navy">Programs:</h4>
                <ul className="space-y-1">
                  {faculty.programs.map((program, index) => (
                    <li key={index} className="flex items-center">
                      <span className="w-2 h-2 bg-aul-blue rounded-full mr-2"></span>
                      <Link
                        to={`${faculty.url}/${program.toLowerCase().replace(/\s+/g, '-')}`}
                        className="text-gray-700 hover:text-aul-blue transition-colors"
                      >
                        {program}
                      </Link>
                    </li>
                  ))}
                </ul>
              </CardContent>
              <div className="p-6 pt-0 mt-auto">
                <Link
                  to={faculty.url}
                  className="inline-flex items-center text-aul-blue font-medium hover:text-aul-navy transition-colors"
                >
                  Explore Faculty <ArrowRight size={16} className="ml-1" />
                </Link>
              </div>
            </Card>
          ))}
        </div>

        <div className="mt-12 text-center">
          <Button asChild>
            <Link to="/academics/programs">View All Programs</Link>
          </Button>
        </div>
      </div>
    </section>
  );
};

export default AcademicPrograms;
