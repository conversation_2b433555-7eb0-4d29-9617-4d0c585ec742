import React from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Calendar, Clock, User, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { Badge } from '@/components/ui/badge';

const EnglishDayBeirut = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow py-12">
        <div className="container mx-auto px-4 max-w-4xl">
          <div className="mb-6">
            <Button variant="ghost" size="sm" asChild className="mb-4">
              <Link to="/news-events/news" className="flex items-center text-gray-600 hover:text-aul-navy">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to News
              </Link>
            </Button>

            <h1 className="text-3xl md:text-4xl font-bold text-aul-navy mb-4">English Department Celebrates English Day at AUL Beirut</h1>

            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6">
              <Badge variant="secondary" className="bg-aul-blue/10 text-aul-navy hover:bg-aul-blue/20 border border-aul-blue/20">
                Academic Events
              </Badge>
              <div className="flex items-center">
                <Calendar className="mr-1 h-4 w-4" />
                <span>January 15, 2025</span>
              </div>
              <div className="flex items-center">
                <User className="mr-1 h-4 w-4" />
                <span>English Department</span>
              </div>
            </div>
          </div>

          <div className="mb-8">
            <img
              src="/images/news2.jpg"
              alt="English Department Celebrates English Day at AUL Beirut"
              className="w-full h-auto rounded-lg shadow-md"
            />
          </div>

          <div className="prose prose-lg max-w-none">
            <p className="text-lg leading-relaxed mb-6">
              In the presence of the President of AUL Prof. Bassam Al-Hajjar, vice-president Dr. Adnan Chaaban, the Dean of the Faculty of Humanities Dr. Amina Al Mir and the Chairperson of the Department of English language and Literature Dr. Mahmoud Makkouk and many English Doctors along with the presence of many students, the English Department celebrated the occasion of the English Day at AUL Beirut in the Conference Room.
            </p>

            <h2 className="text-2xl font-bold text-aul-navy mb-4">Presidential Address</h2>
            <p className="mb-4">
              Prof. Bassam Al-Hajjar showed the importance of the English Language and advised all students to work hard on learning the Language for it is the main gate for all other majors and for finding quick jobs. The President emphasized that English proficiency is essential for academic success and career advancement in today's globalized world.
            </p>

            <h2 className="text-2xl font-bold text-aul-navy mb-4">Dean's Perspective on Cultural Exchange</h2>
            <p className="mb-4">
              Dean Amina Al-Mir asserted that such celebrations emphasize the role of the university in preparing the right academic atmosphere on the basis of cultural exchange through learning other's languages like the English Language. She highlighted how language learning fosters international understanding and opens doors to global opportunities.
            </p>

            <h2 className="text-2xl font-bold text-aul-navy mb-4">Academic Excellence and Professional Development</h2>
            <p className="mb-4">
              Dr. Mahmoud Makkouk showed how important it is to master the English Language for both personal and professional advancement, whether a person is seeking an advancement in education, business, travel, or technology. His presentation demonstrated the practical applications of English language skills across various fields.
            </p>

            <h2 className="text-2xl font-bold text-aul-navy mb-4">Student Participation</h2>
            <p className="mb-4">
              Student Haifaa Shehab stressed the idea that Language is not just words, it is a culture that acts as a bridge between heritage and identity. Her contribution highlighted the deeper cultural significance of language learning and its role in connecting different communities.
            </p>

            <h2 className="text-2xl font-bold text-aul-navy mb-4">Special Book Signing</h2>
            <p className="mb-4">
              The celebration ended in having Dr. Makkouk sign his new grammar book, providing students and faculty with an opportunity to engage with the latest academic resources in English language learning. This special moment marked the culmination of a successful English Day celebration.
            </p>

            <div className="mt-8 p-6 bg-aul-gray rounded-lg">
              <h3 className="text-xl font-semibold text-aul-navy mb-3">About the English Department</h3>
              <p className="text-gray-700">
                The English Department at AUL is committed to providing comprehensive language education that prepares students for global communication and academic excellence. Through events like English Day, the department fosters a deeper appreciation for the English language and its cultural significance.
              </p>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default EnglishDayBeirut;
