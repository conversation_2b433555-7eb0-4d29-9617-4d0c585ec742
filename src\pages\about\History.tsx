
import React from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent } from '@/components/ui/card';

const History = () => {
  const timeline = [
    {
      year: "1975",
      title: "Foundation",
      description: "AUL University was established with a vision to provide quality education in Lebanon."
    },
    {
      year: "1985",
      title: "Expansion",
      description: "Added new faculties including Engineering and Business Administration."
    },
    {
      year: "1995",
      title: "Research Centers",
      description: "Established multiple research centers focusing on technology and innovation."
    },
    {
      year: "2005",
      title: "International Recognition",
      description: "Achieved international accreditation and formed global partnerships."
    },
    {
      year: "2015",
      title: "Modern Campus",
      description: "Completed major campus modernization and expansion projects."
    },
    {
      year: "2025",
      title: "Digital Transformation",
      description: "Implemented cutting-edge digital learning platforms and smart campus initiatives."
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-4xl font-bold text-aul-navy mb-8">Our History</h1>
          
          <div className="space-y-8">
            {timeline.map((item, index) => (
              <Card key={index} className="relative">
                <CardContent className="pt-6">
                  <div className="flex items-start gap-6">
                    <div className="text-2xl font-bold text-aul-gold whitespace-nowrap">
                      {item.year}
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold mb-2">{item.title}</h3>
                      <p className="text-gray-600">{item.description}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default History;
