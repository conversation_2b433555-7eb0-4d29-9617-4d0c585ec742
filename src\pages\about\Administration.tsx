
import React from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';

const Administration = () => {
  const leadership = [
    {
      name: "Dr. <PERSON>",
      title: "President",
      description: "Leading AUL's strategic vision and institutional development since 2020."
    },
    {
      name: "Prof. <PERSON>",
      title: "Vice President for Academic Affairs",
      description: "Overseeing curriculum development and academic excellence initiatives."
    },
    {
      name: "Dr. <PERSON>",
      title: "Dean of Research",
      description: "Directing research initiatives and fostering innovation across departments."
    },
    {
      name: "Dr. <PERSON>",
      title: "Dean of Student Affairs",
      description: "Ensuring student success and coordinating support services."
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-4xl font-bold text-aul-navy mb-8">University Administration</h1>
          
          <p className="text-lg text-gray-600 mb-8">
            Our leadership team brings together experienced educators and administrators 
            committed to advancing AUL's mission of excellence in education and research.
          </p>

          <div className="grid md:grid-cols-2 gap-8">
            {leadership.map((leader, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle>{leader.name}</CardTitle>
                  <div className="text-aul-gold font-medium">{leader.title}</div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">{leader.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Administration;
