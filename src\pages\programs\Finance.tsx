
import React from 'react';
import ProgramDetail from '@/components/programs/ProgramDetail';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

const Finance = () => {
  const programData = {
    name: "Bachelor of Science in Finance",
    description: "Our Finance program provides a comprehensive understanding of financial markets, investment strategies, and corporate finance. Students develop analytical skills to make sound financial decisions in various contexts.",
    duration: "3 years",
    faculty: "Faculty of Business",
    degreeAwarded: "Bachelor of Science (BSc) in Finance",
    careerOpportunities: [
      "Financial Analyst",
      "Investment Banker",
      "Portfolio Manager",
      "Risk Management Specialist",
      "Financial Consultant",
      "Corporate Finance Officer",
      "Financial Planner",
      "Credit Analyst"
    ],
    courseHighlights: [
      {
        year: "First Year",
        courses: [
          "Principles of Finance",
          "Financial Accounting",
          "Economics for Business",
          "Business Mathematics",
          "Introduction to Business Law"
        ]
      },
      {
        year: "Second Year",
        courses: [
          "Corporate Finance",
          "Financial Markets and Institutions",
          "Investment Analysis",
          "Financial Reporting and Analysis",
          "Business Statistics"
        ]
      },
      {
        year: "Third Year",
        courses: [
          "Portfolio Management",
          "Financial Risk Management",
          "International Finance",
          "Financial Derivatives",
          "Capstone Project"
        ]
      }
    ],
    admissionRequirements: [
      "High School Diploma or equivalent",
      "Minimum GPA of 2.8",
      "Strong analytical and quantitative skills",
      "Basic understanding of economics (preferred)",
      "English language proficiency",
      "Statement of purpose"
    ],
    programObjectives: [
      "Develop strong understanding of financial theories and concepts",
      "Master financial analysis and valuation techniques",
      "Apply financial knowledge to make informed investment decisions",
      "Understand global financial markets and international finance",
      "Develop risk management strategies for various scenarios",
      "Prepare for careers in financial services, banking, and consulting"
    ]
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow">
        <ProgramDetail program={programData} />
      </main>
      <Footer />
    </div>
  );
};

export default Finance;
