import React from 'react';
import ProgramDetail from '@/components/programs/ProgramDetail';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

const Journalism = () => {
  const programData = {
    name: "Bachelor of Arts in Journalism",
    description: "The Journalism program at AUL is dedicated to developing skilled journalists who can report accurately, ethically, and effectively across various media platforms. Students learn investigative techniques, writing, and multimedia storytelling while developing a strong understanding of media ethics and the role of journalism in society.",
    duration: "3 years (6 semesters)",
    faculty: "Faculty of Arts & Humanities",
    degreeAwarded: "Bachelor of Arts (BA) in Journalism",
    majorHighlights: [
      "Hands-on reporting experience",
      "Student-run news publications",
      "Focus on digital and multimedia journalism",
      "Investigative journalism projects",
      "Media ethics and legal training",
      "Internships with news organizations",
      "Multimedia storytelling techniques",
      "Coverage of real events and issues"
    ],
    careerOpportunities: [
      "News Reporter",
      "Editor",
      "Investigative Journalist",
      "Photojournalist",
      "Digital Content Producer",
      "Foreign Correspondent",
      "Feature Writer",
      "Broadcast Journalist",
      "News Anchor",
      "Editorial Writer",
      "Sports Journalist",
      "Political Reporter",
      "Media Analyst",
      "News Director",
      "Multimedia Journalist"
    ],
    courseHighlights: [
      {
        year: "First Year - Semester 1",
        courses: [
          "JOUR 201: Introduction to Journalism (3 credits)",
          "JOUR 203: News Writing Fundamentals (3 credits)",
          "COMM 201: Introduction to Mass Communication (3 credits)",
          "ENGL 201: English Communication Skills I (3 credits)",
          "HIST 201: History of Journalism (3 credits)"
        ]
      },
      {
        year: "First Year - Semester 2",
        courses: [
          "JOUR 202: Reporting Techniques (3 credits)",
          "JOUR 204: Media Ethics and Law (3 credits)",
          "ENGL 202: English Communication Skills II (3 credits)",
          "POLI 201: Political Systems (3 credits)",
          "COMP 201: Digital Tools for Journalists (3 credits)"
        ]
      },
      {
        year: "Second Year - Semester 3",
        courses: [
          "JOUR 301: Investigative Journalism (3 credits)",
          "JOUR 303: Feature Writing (3 credits)",
          "JOUR 305: Photojournalism (3 credits)",
          "JOUR 307: Media Literacy (3 credits)",
          "ECON 301: Economics for Journalists (3 credits)"
        ]
      },
      {
        year: "Second Year - Semester 4",
        courses: [
          "JOUR 302: Digital Journalism (3 credits)",
          "JOUR 304: Broadcast News Writing (3 credits)",
          "JOUR 306: Data Journalism (3 credits)",
          "JOUR 308: Editorial Writing (3 credits)",
          "SOCI 301: Social Issues Reporting (3 credits)"
        ]
      },
      {
        year: "Third Year - Semester 5",
        courses: [
          "JOUR 401: Advanced Reporting (3 credits)",
          "JOUR 403: International Journalism (3 credits)",
          "JOUR 405: Specialized Reporting (3 credits)",
          "JOUR 407: Multimedia Storytelling (3 credits)",
          "JOUR 409: Internship I (3 credits)"
        ]
      },
      {
        year: "Third Year - Semester 6",
        courses: [
          "JOUR 402: Journalism Capstone Project (4 credits)",
          "JOUR 404: Media Management (3 credits)",
          "JOUR 406: Specialized Elective (3 credits)",
          "JOUR 408: Portfolio Development (3 credits)",
          "JOUR 410: Internship II (3 credits)"
        ]
      }
    ],
    admissionRequirements: [
      "Lebanese Baccalaureate or equivalent",
      "Minimum overall average of 12/20 or equivalent",
      "English language proficiency (TOEFL score of 500+ or equivalent)",
      "Personal interview with the department",
      "Writing sample",
      "Basic computer literacy"
    ],
    programObjectives: [
      "Develop strong reporting and writing skills for various media platforms",
      "Build ethical awareness and professional responsibility in journalism",
      "Master multimedia storytelling techniques",
      "Understand the role of journalism in society and democracy",
      "Develop investigative and research capabilities",
      "Prepare for successful careers in the evolving field of journalism",
      "Foster critical thinking and analytical abilities",
      "Build a professional portfolio of journalistic work"
    ],
    videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ"
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow">
        <ProgramDetail program={programData} />
      </main>
      <Footer />
    </div>
  );
};

export default Journalism;
