
import React from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const StudentClubs = () => {
  const clubs = [
    {
      category: "Academic & Professional",
      items: [
        "Computer Science Society",
        "Engineering Club",
        "Business Leaders Association",
        "Mathematics Club",
        "Future Medical Professionals"
      ]
    },
    {
      category: "Cultural & Social",
      items: [
        "International Students Association",
        "Cultural Exchange Club",
        "Music Society",
        "Art & Design Club",
        "Drama Club"
      ]
    },
    {
      category: "Sports & Recreation",
      items: [
        "Soccer Club",
        "Basketball Team",
        "Swimming Club",
        "Tennis Club",
        "Hiking & Adventure Club"
      ]
    },
    {
      category: "Community Service",
      items: [
        "Volunteer Club",
        "Environmental Society",
        "Community Outreach Club",
        "Red Cross Youth Club",
        "Charity Foundation"
      ]
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-4xl font-bold text-aul-navy mb-8">Student Clubs & Organizations</h1>
          
          <p className="text-lg text-gray-600 mb-8">
            Get involved in campus life by joining one of our many student clubs and organizations. 
            These groups provide opportunities for leadership, personal growth, and building lasting friendships.
          </p>

          <div className="grid md:grid-cols-2 gap-8">
            {clubs.map((category, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle>{category.category}</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="list-disc list-inside space-y-2">
                    {category.items.map((club, idx) => (
                      <li key={idx} className="text-gray-600">{club}</li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default StudentClubs;
