
import React from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Utensils } from 'lucide-react';

const Dining = () => {
  const diningOptions = [
    {
      name: "Main Cafeteria",
      location: "Student Center, Ground Floor",
      hours: "7:30 AM - 8:00 PM",
      description: "Our main dining facility offering international cuisine, healthy options, and daily specials."
    },
    {
      name: "Coffee House",
      location: "Library Building",
      hours: "8:00 AM - 10:00 PM",
      description: "Serving premium coffee, pastries, and light snacks in a cozy atmosphere."
    },
    {
      name: "Express Café",
      location: "Engineering Building",
      hours: "7:30 AM - 6:00 PM",
      description: "Quick bites and beverages for students on the go."
    },
    {
      name: "Health Bar",
      location: "Sports Complex",
      hours: "8:00 AM - 7:00 PM",
      description: "Nutritious smoothies, protein shakes, and healthy snacks."
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow py-12">
        <div className="container mx-auto px-4">
          <div className="flex items-center gap-4 mb-8">
            <Utensils className="w-12 h-12 text-aul-gold" />
            <h1 className="text-4xl font-bold text-aul-navy">Campus Dining</h1>
          </div>
          
          <p className="text-lg text-gray-600 mb-8">
            AUL offers diverse dining options across campus, ensuring students have access 
            to nutritious and delicious meals throughout the day.
          </p>

          <div className="grid md:grid-cols-2 gap-8">
            {diningOptions.map((option, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle>{option.name}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <p><strong>Location:</strong> {option.location}</p>
                    <p><strong>Hours:</strong> {option.description}</p>
                    <p className="text-gray-600">{option.description}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Dining;
