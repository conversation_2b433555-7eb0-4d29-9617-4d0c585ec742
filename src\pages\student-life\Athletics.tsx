
import React from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Trophy, Users, Award, Target } from 'lucide-react';

const Athletics = () => {
  const sports = [
    {
      category: "Varsity Sports",
      image: "/images/Athletics & Recreation/IMG_3119.JPG",
      teams: [
        "Men's Basketball",
        "Women's Basketball",
        "Men's Soccer",
        "Women's Soccer",
        "Swimming"
      ]
    },
    {
      category: "Intramural Sports",
      image: "/images/Athletics & Recreation/IMG_3131.JPG",
      teams: [
        "Volleyball",
        "Tennis",
        "Table Tennis",
        "Badminton",
        "Futsal"
      ]
    },
    {
      category: "Fitness Programs",
      image: "/images/Athletics & Recreation/IMG_4181.JPG",
      teams: [
        "Yoga Classes",
        "Strength Training",
        "Cardio Sessions",
        "CrossFit",
        "Dance Fitness"
      ]
    }
  ];

  const facilitiesImages = [
    {
      src: "/images/Athletics & Recreation/FB_IMG_1734939178387.jpg",
      alt: "AUL Sports Facilities"
    },
    {
      src: "/images/Athletics & Recreation/FB_IMG_1734939376322.jpg",
      alt: "Athletic Training"
    },
    {
      src: "/images/Athletics & Recreation/FB_IMG_1734939446635.jpg",
      alt: "Recreation Activities"
    },
    {
      src: "/images/Athletics & Recreation/FB_IMG_1734939460432.jpg",
      alt: "Sports Equipment"
    },
    {
      src: "/images/Athletics & Recreation/FB_IMG_1734939690192.jpg",
      alt: "Team Sports"
    },
    {
      src: "/images/Athletics & Recreation/FB_IMG_1734939826952.jpg",
      alt: "Athletic Events"
    },
    {
      src: "/images/Athletics & Recreation/FB_IMG_1734940944382.jpg",
      alt: "Sports Competition"
    },
    {
      src: "/images/Athletics & Recreation/FB_IMG_1734941378044.jpg",
      alt: "Recreation Center"
    },
    {
      src: "/images/Athletics & Recreation/FB_IMG_1734941472245.jpg",
      alt: "Fitness Activities"
    },
    {
      src: "/images/Athletics & Recreation/FB_IMG_1734941621665.jpg",
      alt: "Sports Programs"
    },
    {
      src: "/images/Athletics & Recreation/IMG_7145.JPG",
      alt: "Athletic Achievements"
    },
    {
      src: "/images/Athletics & Recreation/IMG_7260.JPG",
      alt: "Sports Excellence"
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow py-12">
        <div className="container mx-auto px-4">
          <div className="flex items-center gap-4 mb-8">
            <Trophy className="w-12 h-12 text-aul-gold" />
            <h1 className="text-4xl font-bold text-aul-navy">Athletics & Recreation</h1>
          </div>

          {/* Hero Image Gallery */}
          <section className="mb-12">
            <Card className="overflow-hidden">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                {facilitiesImages.slice(0, 8).map((image, index) => (
                  <div key={index} className="relative aspect-square overflow-hidden">
                    <img
                      src={image.src}
                      alt={image.alt}
                      className="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
                    />
                  </div>
                ))}
              </div>
              <CardContent className="py-6">
                <h2 className="text-2xl font-bold text-aul-navy mb-4">About AUL Athletics</h2>
                <p className="text-gray-700 mb-4">
                  AUL offers a comprehensive athletics program that promotes physical fitness,
                  teamwork, and competitive excellence. Our state-of-the-art facilities and
                  experienced coaching staff provide students with opportunities to excel in
                  various sports and recreational activities.
                </p>
                <p className="text-gray-700">
                  Join our teams and be part of our winning tradition while developing leadership
                  skills, building lifelong friendships, and maintaining a healthy lifestyle.
                </p>
              </CardContent>
            </Card>
          </section>

          {/* Sports Categories */}
          <section className="mb-12">
            <h2 className="text-2xl font-bold text-aul-navy mb-6">Our Programs</h2>
            <div className="grid md:grid-cols-3 gap-8">
              {sports.map((category, index) => (
                <Card key={index} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="relative aspect-video">
                    <img
                      src={category.image}
                      alt={category.category}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-black/20"></div>
                  </div>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      {index === 0 && <Trophy className="w-5 h-5 text-aul-gold" />}
                      {index === 1 && <Users className="w-5 h-5 text-aul-gold" />}
                      {index === 2 && <Target className="w-5 h-5 text-aul-gold" />}
                      {category.category}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {category.teams.map((team, idx) => (
                        <li key={idx} className="flex items-center gap-2">
                          <span className="w-2 h-2 bg-aul-gold rounded-full"></span>
                          <span className="text-gray-600">{team}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>

          {/* Additional Facilities Gallery */}
          <section className="mb-12">
            <h2 className="text-2xl font-bold text-aul-navy mb-6">Facilities & Activities</h2>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {facilitiesImages.slice(8).map((image, index) => (
                <div key={index} className="relative aspect-square overflow-hidden rounded-lg">
                  <img
                    src={image.src}
                    alt={image.alt}
                    className="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300">
                    <div className="absolute bottom-2 left-2 text-white text-sm font-medium">
                      {image.alt}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </section>

          {/* Achievements Section */}
          <section>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="w-6 h-6 text-aul-gold" />
                  Athletic Achievements
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-8">
                  <div>
                    <h3 className="text-xl font-semibold text-aul-navy mb-4">Recent Accomplishments</h3>
                    <ul className="space-y-3">
                      <li className="flex items-start gap-3">
                        <Trophy className="w-5 h-5 text-aul-gold mt-0.5" />
                        <div>
                          <p className="font-medium">Basketball Championship</p>
                          <p className="text-gray-600 text-sm">Regional University League Winners 2024</p>
                        </div>
                      </li>
                      <li className="flex items-start gap-3">
                        <Trophy className="w-5 h-5 text-aul-gold mt-0.5" />
                        <div>
                          <p className="font-medium">Swimming Excellence</p>
                          <p className="text-gray-600 text-sm">Multiple individual records broken</p>
                        </div>
                      </li>
                      <li className="flex items-start gap-3">
                        <Trophy className="w-5 h-5 text-aul-gold mt-0.5" />
                        <div>
                          <p className="font-medium">Soccer Tournament</p>
                          <p className="text-gray-600 text-sm">Inter-university competition finalists</p>
                        </div>
                      </li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-aul-navy mb-4">Join Our Teams</h3>
                    <p className="text-gray-700 mb-4">
                      Whether you're a seasoned athlete or just starting your fitness journey,
                      AUL Athletics has something for everyone. Our programs are designed to
                      accommodate all skill levels and interests.
                    </p>
                    <div className="space-y-2">
                      <p className="text-sm text-gray-600">
                        <strong>Tryouts:</strong> Beginning of each semester
                      </p>
                      <p className="text-sm text-gray-600">
                        <strong>Contact:</strong> <EMAIL>
                      </p>
                      <p className="text-sm text-gray-600">
                        <strong>Office Hours:</strong> Monday-Friday, 9:00 AM - 5:00 PM
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </section>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Athletics;
