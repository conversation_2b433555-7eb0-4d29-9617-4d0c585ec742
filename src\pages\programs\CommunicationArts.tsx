import React from 'react';
import ProgramDetail from '@/components/programs/ProgramDetail';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

const CommunicationArts = () => {
  const programData = {
    name: "Bachelor of Arts in Communication Arts",
    description: "The Communication Arts program at AUL provides students with a comprehensive education in various aspects of communication including journalism, film, radio, and television. Our curriculum combines theoretical knowledge with practical applications, preparing graduates for successful careers in the dynamic field of media and communication.",
    duration: "3 years (6 semesters)",
    faculty: "Faculty of Arts & Humanities",
    degreeAwarded: "Bachelor of Arts (BA) in Communication Arts",
    majorHighlights: [
      "Hands-on experience with professional media equipment",
      "Industry-standard production studios",
      "Opportunities to create real media projects",
      "Focus on both theoretical and practical aspects",
      "Preparation for diverse media careers",
      "Student-run media productions",
      "Industry professionals as guest lecturers",
      "Internship opportunities with media organizations"
    ],
    careerOpportunities: [
      "Journalist",
      "Film/TV Producer",
      "Radio Broadcaster",
      "Media Analyst",
      "Content Creator",
      "Public Relations Specialist",
      "Social Media Manager",
      "Documentary Filmmaker",
      "Media Planner",
      "Communications Director",
      "Advertising Specialist",
      "Digital Media Strategist",
      "Corporate Communications Manager",
      "Media Relations Coordinator",
      "Broadcast Journalist"
    ],
    courseHighlights: [
      {
        year: "First Year - Semester 1",
        courses: [
          "COMM 201: Introduction to Mass Communication (3 credits)",
          "COMM 203: Media Writing Fundamentals (3 credits)",
          "ENGL 201: English Communication Skills I (3 credits)",
          "ARAB 201: Arabic Essay Reading and Writing (2 credits)",
          "CSCI 201: Computer Literacy (3 credits)"
        ]
      },
      {
        year: "First Year - Semester 2",
        courses: [
          "COMM 202: Communication Theory (3 credits)",
          "COMM 204: Visual Communication (3 credits)",
          "ENGL 202: English Communication Skills II (3 credits)",
          "CULT 201: Cultural Studies (3 credits)",
          "HIST 201: History of Media (3 credits)"
        ]
      },
      {
        year: "Second Year - Semester 3",
        courses: [
          "COMM 301: Media Ethics and Law (3 credits)",
          "COMM 303: Digital Media Production (3 credits)",
          "COMM 305: Public Speaking (3 credits)",
          "JOUR 301: News Reporting and Writing (3 credits)",
          "FILM 301: Introduction to Film and TV Production (3 credits)"
        ]
      },
      {
        year: "Second Year - Semester 4",
        courses: [
          "COMM 302: Media Research Methods (3 credits)",
          "COMM 304: Public Relations (3 credits)",
          "JOUR 302: Digital Journalism (3 credits)",
          "FILM 302: Video Editing and Post-Production (3 credits)",
          "RADIO 301: Radio Broadcasting (3 credits)"
        ]
      },
      {
        year: "Third Year - Semester 5",
        courses: [
          "COMM 401: Media Management (3 credits)",
          "COMM 403: International Communication (3 credits)",
          "COMM 405: Social Media Strategies (3 credits)",
          "COMM 407: Specialized Elective I (3 credits)",
          "COMM 409: Internship I (3 credits)"
        ]
      },
      {
        year: "Third Year - Semester 6",
        courses: [
          "COMM 402: Media Criticism (3 credits)",
          "COMM 404: Communication and Society (3 credits)",
          "COMM 406: Specialized Elective II (3 credits)",
          "COMM 408: Capstone Project (4 credits)",
          "COMM 410: Internship II (3 credits)"
        ]
      }
    ],
    admissionRequirements: [
      "Lebanese Baccalaureate or equivalent",
      "Minimum overall average of 12/20 or equivalent",
      "English language proficiency (TOEFL score of 500+ or equivalent)",
      "Personal interview with the department",
      "Portfolio of creative work (optional but recommended)"
    ],
    programObjectives: [
      "Develop strong communication skills across various media platforms",
      "Build critical thinking and analytical abilities for media content",
      "Master technical skills in media production and content creation",
      "Understand ethical and legal aspects of media and communication",
      "Develop research capabilities for media and communication studies",
      "Prepare for successful careers in the diverse field of media",
      "Foster creativity and innovation in communication practices",
      "Develop understanding of media's role in society and culture"
    ],
    videoUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ"
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow">
        <ProgramDetail program={programData} />
      </main>
      <Footer />
    </div>
  );
};

export default CommunicationArts;
