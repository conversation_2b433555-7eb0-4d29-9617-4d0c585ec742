
import React from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Building } from 'lucide-react';

const Facilities = () => {
  const facilities = [
    {
      name: "Libraries",
      description: "Modern libraries equipped with digital resources, study rooms, and collaborative spaces.",
      features: ["24/7 Study Areas", "Digital Catalogs", "Research Databases"]
    },
    {
      name: "Sports Complex",
      description: "State-of-the-art athletic facilities for various sports and fitness activities.",
      features: ["Olympic Pool", "Fitness Center", "Indoor Courts"]
    },
    {
      name: "Student Center",
      description: "A hub for student activities, dining, and social gatherings.",
      features: ["Meeting Rooms", "Event Spaces", "Recreation Areas"]
    },
    {
      name: "Research Labs",
      description: "Advanced laboratories supporting research across multiple disciplines.",
      features: ["Science Labs", "Computer Labs", "Engineering Workshops"]
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow py-12">
        <div className="container mx-auto px-4">
          <div className="flex items-center gap-4 mb-8">
            <Building className="w-12 h-12 text-aul-gold" />
            <h1 className="text-4xl font-bold text-aul-navy">Campus Facilities</h1>
          </div>
          
          <p className="text-lg text-gray-600 mb-8">
            AUL provides world-class facilities to support academic excellence, 
            research, and student life activities.
          </p>

          <div className="grid md:grid-cols-2 gap-8">
            {facilities.map((facility, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle>{facility.name}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4">{facility.description}</p>
                  <div className="flex flex-wrap gap-2">
                    {facility.features.map((feature, idx) => (
                      <span 
                        key={idx}
                        className="bg-aul-navy text-white px-3 py-1 rounded-full text-sm"
                      >
                        {feature}
                      </span>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Facilities;
