import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import {
  Menu,
  X,
  ChevronDown,
  Search,
  Globe
} from 'lucide-react';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuList,
  NavigationMenuTrigger,
  NavigationMenuLink,
} from "@/components/ui/navigation-menu";
import { Button } from "@/components/ui/button";
import { cn } from '@/lib/utils';

const navigationItems = [
  {
    title: "About",
    href: "/about",
    subItems: [
      { title: "Mission & Vision", href: "/about/mission-vision" },
      { title: "History", href: "/about/history" },
      { title: "Accreditations", href: "/about/accreditations" },
      { title: "Affiliations", href: "/about/affiliations" },
      { title: "Administration", href: "/about/administration" },
      { title: "Campus Locations", href: "/about/campus-locations" },
      { title: "Strategic Plan", href: "/about/strategic-plan" }
    ]
  },
  {
    title: "Admissions",
    href: "/admissions",
    subItems: [
      { title: "Undergraduate", href: "/admissions/undergraduate" },
      { title: "Graduate", href: "/admissions/graduate" },
      { title: "International Students", href: "/admissions/international" },
      { title: "Financial Aid", href: "/admissions/financial-aid" },
      { title: "Scholarships", href: "/admissions/scholarships" },
      { title: "Application Requirements", href: "/admissions/requirements" },
      { title: "Online Application", href: "/admissions/apply" }
    ]
  },
  {
    title: "Academics",
    href: "/academics",
    subItems: [
      { title: "Faculties", href: "/academics/faculties" },
      { title: "Programs", href: "/academics/programs" },
      { title: "Calendar", href: "/academics/calendar" },
      { title: "Library", href: "/academics/library" }
    ]
  },
  {
    title: "Faculties",
    href: "/faculties",
    subItems: [
      { title: "Faculty of Sciences", href: "/faculties/sciences" },
      { title: "Faculty of Engineering", href: "/faculties/engineering" },
      { title: "Faculty of Business", href: "/faculties/business" },
      { title: "Faculty of Arts", href: "/faculties/arts" },
      { title: "Faculty of Law", href: "/faculties/law" }
    ]
  },
  {
    title: "Research",
    href: "/research",
    subItems: [
      { title: "Research Centers", href: "/research/centers" },
      { title: "Projects", href: "/research/projects" },
      { title: "Publications", href: "/research/publications" },
      { title: "Grants", href: "/research/grants" },
      { title: "Ethics Committee", href: "/research/ethics" },
      { title: "Partnerships", href: "/research/partnerships" }
    ]
  },
  {
    title: "Student Life",
    href: "/student-life",
    subItems: [
      { title: "Clubs & Societies", href: "/student-life/clubs" },
      { title: "Housing", href: "/student-life/housing" },
      { title: "Dining", href: "/student-life/dining" },
      { title: "Athletics", href: "/student-life/athletics" },
      { title: "Counseling Services", href: "/student-life/counseling" },
      { title: "Campus Facilities", href: "/student-life/facilities" },
      { title: "Student Success Stories", href: "/student-life/success-stories" }
    ]
  },

];

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeLanguage, setActiveLanguage] = useState("English");

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const changeLanguage = (language: string) => {
    setActiveLanguage(language);
    // In a real implementation, this would change the site language
  };

  return (
    <header className="bg-white shadow-md">
      {/* Top bar with secondary navigation */}
      <div className="bg-aul-navy text-white py-2">
        <div className="aul-container flex justify-between items-center">
          <div className="flex items-center space-x-4 text-sm">
            <Link to="/for-parents" className="hover:text-aul-gold transition-colors">For Parents</Link>
            <Link to="/for-faculty" className="hover:text-aul-gold transition-colors">For Faculty</Link>
            <Link to="/for-staff" className="hover:text-aul-gold transition-colors">For Staff</Link>
            <Link to="/alumni" className="hover:text-aul-gold transition-colors">Alumni</Link>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1 cursor-pointer group">
              <Globe size={16} />
              <div className="relative">
                <span className="mr-1">{activeLanguage}</span>
                <ChevronDown size={14} />
                <div className="absolute right-0 mt-2 bg-white text-aul-navy rounded-md shadow-lg py-1 z-50 w-28 hidden group-hover:block">
                  <button
                    onClick={() => changeLanguage("English")}
                    className="block px-4 py-1 hover:bg-gray-100 w-full text-left"
                  >
                    English
                  </button>
                  <button
                    onClick={() => changeLanguage("العربية")}
                    className="block px-4 py-1 hover:bg-gray-100 w-full text-left"
                  >
                    العربية
                  </button>
                  <button
                    onClick={() => changeLanguage("Français")}
                    className="block px-4 py-1 hover:bg-gray-100 w-full text-left"
                  >
                    Français
                  </button>
                </div>
              </div>
            </div>
            <Link to="/search" className="hover:text-aul-gold transition-colors">
              <Search size={16} />
            </Link>
          </div>
        </div>
      </div>

      {/* Main navigation */}
      <div className="aul-container py-6">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <Link to="/" className="flex items-center mr-8 group">
            <img
              src="/lovable-uploads/84495c26-350f-4fc4-ac8e-0766d47be600.png"
              alt="AUL University Logo"
              className="h-20 w-20 md:h-24 md:w-24 rounded-full object-cover bg-white border-4 border-aul-navy shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
              style={{ background: "white", imageRendering: "-webkit-optimize-contrast" }}
            />
            <div className="ml-3 hidden sm:block">
              <div className="text-2xl md:text-3xl font-bold text-aul-navy group-hover:text-aul-blue transition-colors duration-300">
                AUL
              </div>
              <div className="text-xs md:text-sm text-gray-600 -mt-1">
                University
              </div>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-2 flex-1">
            <NavigationMenu>
              <NavigationMenuList className="border-none">
                <NavigationMenuItem>
                  <Link to="/">
                    <NavigationMenuLink className={cn(
                      "group inline-flex h-10 w-max items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors hover:bg-aul-navy hover:text-white focus:bg-aul-navy focus:text-white focus:outline-none disabled:pointer-events-none disabled:opacity-50 border-none"
                    )}>
                      Home
                    </NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>

                {navigationItems.map((item, index) => (
                  <NavigationMenuItem key={index}>
                    <NavigationMenuTrigger className="text-sm font-medium">
                      {item.title}
                    </NavigationMenuTrigger>
                    <NavigationMenuContent className="bg-white border shadow-lg rounded-md overflow-hidden">
                      <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                        {item.subItems.map((subItem, subIndex) => (
                          <li key={subIndex}>
                            <NavigationMenuLink asChild>
                              <Link
                                to={subItem.href}
                                className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                              >
                                <div className="text-sm font-medium leading-none">{subItem.title}</div>
                              </Link>
                            </NavigationMenuLink>
                          </li>
                        ))}
                      </ul>
                    </NavigationMenuContent>
                  </NavigationMenuItem>
                ))}

                <NavigationMenuItem>
                  <Link to="/contact">
                    <NavigationMenuLink className={cn(
                      "group inline-flex h-10 w-max items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors hover:bg-aul-navy hover:text-white focus:bg-aul-navy focus:text-white focus:outline-none disabled:pointer-events-none disabled:opacity-50"
                    )}>
                      Contact Us
                    </NavigationMenuLink>
                  </Link>
                </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenu>

            <div className="flex space-x-2">
              <Button variant="default" size="sm" className="bg-aul-red hover:bg-red-700">
                Apply Now
              </Button>
              <Button variant="outline" size="sm">
                Virtual Tour
              </Button>
            </div>
          </div>

          {/* Mobile menu button */}
          <button
            onClick={toggleMenu}
            className="lg:hidden text-aul-navy focus:outline-none"
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      {isMenuOpen && (
        <div className="lg:hidden bg-white fixed inset-0 z-50 overflow-y-auto pt-16">
          <div className="apy-container py-4">
            <div className="space-y-1">
              <Link
                to="/"
                className="block py-2 text-aul-navy font-medium border-b border-gray-200"
                onClick={() => setIsMenuOpen(false)}
              >
                Home
              </Link>

              {navigationItems.map((item, index) => (
                <div key={index} className="py-2 border-b border-gray-200">
                  <Link
                    to={item.href}
                    className="block text-aul-navy font-medium"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {item.title}
                  </Link>
                  <div className="mt-1 ml-4 space-y-1">
                    {item.subItems.map((subItem, subIndex) => (
                      <Link
                        key={subIndex}
                        to={subItem.href}
                        className="block py-1 text-sm text-gray-700 hover:text-aul-navy"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        {subItem.title}
                      </Link>
                    ))}
                  </div>
                </div>
              ))}

              <Link
                to="/contact"
                className="block py-2 text-aul-navy font-medium border-b border-gray-200"
                onClick={() => setIsMenuOpen(false)}
              >
                Contact Us
              </Link>

              <div className="pt-4 space-y-2">
                <Button variant="default" className="w-full bg-aul-red hover:bg-red-700">
                  Apply Now
                </Button>
                <Button variant="outline" className="w-full">
                  Virtual Tour
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
