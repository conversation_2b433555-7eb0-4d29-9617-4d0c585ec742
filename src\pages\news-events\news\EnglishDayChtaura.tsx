import React from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Calendar, Clock, User, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { Badge } from '@/components/ui/badge';

const EnglishDayChtaura = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow py-12">
        <div className="container mx-auto px-4 max-w-4xl">
          <div className="mb-6">
            <Button variant="ghost" size="sm" asChild className="mb-4">
              <Link to="/news-events/news" className="flex items-center text-gray-600 hover:text-aul-navy">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to News
              </Link>
            </Button>

            <h1 className="text-3xl md:text-4xl font-bold text-aul-navy mb-4">AUL Chtaura Campus Celebrates English Day with Student Presentations</h1>

            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6">
              <Badge variant="secondary" className="bg-aul-blue/10 text-aul-navy hover:bg-aul-blue/20 border border-aul-blue/20">
                Campus Events
              </Badge>
              <div className="flex items-center">
                <Calendar className="mr-1 h-4 w-4" />
                <span>January 12, 2025</span>
              </div>
              <div className="flex items-center">
                <User className="mr-1 h-4 w-4" />
                <span>AUL Chtaura Campus</span>
              </div>
            </div>
          </div>

          <div className="mb-8">
            <img
              src="/images/news3.jpg"
              alt="AUL Chtaura Campus Celebrates English Day"
              className="w-full h-auto rounded-lg shadow-md"
            />
          </div>

          <div className="prose prose-lg max-w-none">
            <p className="text-lg leading-relaxed mb-6">
              AUL Chtaura celebrated the English Day, showcasing the richness and global significance of the English language through a series of engaging student presentations and academic discussions.
            </p>

            <h2 className="text-2xl font-bold text-aul-navy mb-4">Opening Welcome</h2>
            <p className="mb-4">
              The event began with a warm welcome speech by Mr. Murhaf, focusing on the efforts students should have to learn English to be successful. His opening remarks set the tone for a day dedicated to celebrating the importance of English language proficiency in academic and professional contexts.
            </p>

            <h2 className="text-2xl font-bold text-aul-navy mb-4">Student Engagement Activities</h2>
            <p className="mb-4">
              Class 02 distributed informative flyers to the audience highlighting the importance of English language. This interactive approach helped engage all attendees and provided valuable information about the benefits of English language mastery.
            </p>

            <h2 className="text-2xl font-bold text-aul-navy mb-4">Shakespeare and English Heritage</h2>
            <p className="mb-4">
              Dr. Eman Saleh presented a speech showing the historical background of the English Day which is the birthday of Shakespeare, the founder of English language in the 16th century. She then presented a movie (summarized version) to one of Shakespeare's plays, Hamlet, and conducted a discussion with students showing how business students can activate the lessons learnt from the story in their real life.
            </p>

            <h3 className="text-xl font-semibold text-aul-navy mb-3">Lessons from Hamlet</h3>
            <ul className="mb-4 list-disc list-inside">
              <li><strong>Decision Making:</strong> "To be or not to be..." - exploring critical thinking in business decisions</li>
              <li><strong>Planning:</strong> The mouse trap play - strategic planning and execution</li>
              <li><strong>Emotional Intelligence:</strong> Hamlet's psychic situation and stress management</li>
            </ul>

            <h2 className="text-2xl font-bold text-aul-navy mb-4">Student Presentations</h2>
            
            <h3 className="text-xl font-semibold text-aul-navy mb-3">English as a Global Language</h3>
            <p className="mb-4">
              Student Yara AlSayegh (213) presented a speech entitled "English as a Global Language." She narrated her real story how being fluent in English helped her to find a good online job, demonstrating the practical career benefits of English proficiency.
            </p>

            <h3 className="text-xl font-semibold text-aul-navy mb-3">Technology and Business Applications</h3>
            <p className="mb-4">
              Michael Friej (214 class) presented on the importance of English language in technology and business, highlighting how English serves as the lingua franca in these rapidly growing sectors.
            </p>

            <h3 className="text-xl font-semibold text-aul-navy mb-3">Global Communication</h3>
            <p className="mb-4">
              Student Jowe Moussa (216 class) also presented a discussion on the importance of English as a global language, emphasizing its role in international communication and cultural exchange.
            </p>

            <h3 className="text-xl font-semibold text-aul-navy mb-3">Literary Heritage</h3>
            <p className="mb-4">
              Finally, student Robin Yammine made an overview of famous English poets and writers, connecting students with the rich literary tradition of the English language.
            </p>

            <h2 className="text-2xl font-bold text-aul-navy mb-4">Student Response and Future Plans</h2>
            <p className="mb-4">
              The students welcomed the idea and their interaction was good, hoping to do another such communicative activities. The positive response demonstrates the value of interactive learning approaches in language education.
            </p>

            <div className="mt-8 p-6 bg-aul-gray rounded-lg">
              <h3 className="text-xl font-semibold text-aul-navy mb-3">About AUL Chtaura Campus</h3>
              <p className="text-gray-700">
                AUL Chtaura Campus is committed to providing quality education and fostering student engagement through interactive learning experiences. Events like English Day demonstrate the campus's dedication to developing students' language skills and cultural awareness.
              </p>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default EnglishDayChtaura;
