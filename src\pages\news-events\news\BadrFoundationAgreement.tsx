import React from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Calendar, Clock, User, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { Badge } from '@/components/ui/badge';

const BadrFoundationAgreement = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow py-12">
        <div className="container mx-auto px-4 max-w-4xl">
          <div className="mb-6">
            <Button variant="ghost" size="sm" asChild className="mb-4">
              <Link to="/news-events/news" className="flex items-center text-gray-600 hover:text-aul-navy">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to News
              </Link>
            </Button>

            <h1 className="text-3xl md:text-4xl font-bold text-aul-navy mb-4">AUL Signs Cooperation Agreement with Badr Foundation</h1>

            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6">
              <Badge variant="secondary" className="bg-aul-blue/10 text-aul-navy hover:bg-aul-blue/20 border border-aul-blue/20">
                Partnership
              </Badge>
              <div className="flex items-center">
                <Calendar className="mr-1 h-4 w-4" />
                <span>January 20, 2025</span>
              </div>
              <div className="flex items-center">
                <User className="mr-1 h-4 w-4" />
                <span>AUL Administration</span>
              </div>
            </div>
          </div>

          <div className="mb-8">
            <img
              src="/images/news1.jpg"
              alt="AUL Signs Cooperation Agreement with Badr Foundation"
              className="w-full h-auto rounded-lg shadow-md"
            />
          </div>

          <div className="prose prose-lg max-w-none">
            <p className="text-lg leading-relaxed mb-6">
              The University of Arts, Sciences, and Technology in Lebanon (AUL) signed a cooperation agreement with the Badr Foundation, represented by its president, MP Nabil Badr, to support students facing financial difficulties that prevent them from pursuing their university education.
            </p>

            <h2 className="text-2xl font-bold text-aul-navy mb-4">Supporting Student Education</h2>
            <p className="mb-4">
              This significant partnership demonstrates AUL's commitment to making higher education accessible to all qualified students, regardless of their financial circumstances. The cooperation agreement with the Badr Foundation will provide crucial financial support to students who face economic challenges that might otherwise prevent them from completing their university studies.
            </p>

            <h2 className="text-2xl font-bold text-aul-navy mb-4">About the Partnership</h2>
            <p className="mb-4">
              The agreement was signed in the presence of the Chairman of the Board of Trustees and other university officials, marking a milestone in AUL's efforts to expand educational opportunities. MP Nabil Badr, representing the Badr Foundation, expressed his commitment to supporting Lebanese youth in their pursuit of higher education.
            </p>

            <h2 className="text-2xl font-bold text-aul-navy mb-4">Impact on Students</h2>
            <p className="mb-4">
              This partnership will directly benefit students across all faculties at AUL, providing them with the financial assistance needed to focus on their studies without the burden of financial stress. The initiative aligns with AUL's mission to provide quality education and support to its diverse student body.
            </p>

            <h2 className="text-2xl font-bold text-aul-navy mb-4">Looking Forward</h2>
            <p className="mb-4">
              The cooperation between AUL and the Badr Foundation represents a model for how educational institutions and community organizations can work together to support student success. This partnership is expected to have a lasting positive impact on the lives of many students and their families.
            </p>

            <div className="mt-8 p-6 bg-aul-gray rounded-lg">
              <h3 className="text-xl font-semibold text-aul-navy mb-3">About AUL</h3>
              <p className="text-gray-700">
                The University of Arts, Sciences, and Technology in Lebanon (AUL) is committed to providing quality higher education and supporting students in achieving their academic and professional goals. With multiple campuses across Lebanon, AUL serves a diverse student body with comprehensive academic programs.
              </p>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default BadrFoundationAgreement;
