
import React from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Book, BookOpen, Library, Search, Clock, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselPrevious,
  CarouselNext,
} from "@/components/ui/carousel";
import { Separator } from '@/components/ui/separator';

const libraryImages = [
  {
    src: "https://images.unsplash.com/photo-1507842217343-583bb7270b66",
    alt: "University library main hall"
  },
  {
    src: "https://images.unsplash.com/photo-1521587760476-6c12a4b040da",
    alt: "Study area in the library"
  },
  {
    src: "https://images.unsplash.com/photo-1568667256549-094345857637",
    alt: "Book collection"
  },
  {
    src: "https://images.unsplash.com/photo-1524995997946-a1c2e315a42f",
    alt: "Student research area"
  },
];

const LibraryPage = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow py-12">
        <div className="container mx-auto px-4">
          <div className="flex items-center gap-4 mb-8">
            <Library className="w-12 h-12 text-aul-navy" />
            <h1 className="text-4xl font-bold text-aul-navy">University Library</h1>
          </div>

          {/* Library Carousel */}
          <div className="mb-12">
            <Carousel className="w-full" opts={{ loop: true, duration: 20 }}>
              <CarouselContent>
                {libraryImages.map((image, index) => (
                  <CarouselItem key={index}>
                    <div className="relative aspect-[16/7] rounded-lg overflow-hidden">
                      <img 
                        src={image.src} 
                        alt={image.alt} 
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute inset-0 bg-black/20"></div>
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>
              <CarouselPrevious className="left-2 z-20 bg-white text-black hover:bg-gray-200" />
              <CarouselNext className="right-2 z-20 bg-white text-black hover:bg-gray-200" />
            </Carousel>
          </div>

          {/* Library Introduction */}
          <div className="mb-12">
            <div className="max-w-3xl mx-auto text-center">
              <h2 className="text-3xl font-bold mb-4 text-aul-navy">Supporting Academic Excellence</h2>
              <p className="text-lg mb-6">
                The American University of Learning Library is the intellectual heart of our campus, providing access to vast resources, digital collections, and collaborative spaces designed to support research, learning, and innovation.
              </p>
            </div>
          </div>

          {/* Library Features */}
          <div className="mb-12">
            <h2 className="text-2xl font-bold mb-6 text-aul-navy">Library Features</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-center gap-2">
                    <BookOpen className="h-5 w-5 text-aul-gold" />
                    <span>Extensive Collections</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p>Over 1.5 million print volumes, 500,000+ e-books, and subscriptions to thousands of academic journals across all disciplines.</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-center gap-2">
                    <Search className="h-5 w-5 text-aul-gold" />
                    <span>Research Support</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p>Expert librarians available for research consultations, literature reviews, and specialized database searches.</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-center gap-2">
                    <Book className="h-5 w-5 text-aul-gold" />
                    <span>Special Collections</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p>Rare books, manuscripts, and archival materials spanning centuries of knowledge and cultural heritage.</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="h-5 w-5 text-aul-gold" />
                    <span>Extended Hours</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p>Open 24/7 during exam periods with extended hours throughout the academic year to accommodate diverse study schedules.</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-center gap-2">
                    <ArrowRight className="h-5 w-5 text-aul-gold" />
                    <span>Digital Innovation</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p>State-of-the-art digital media labs, 3D printing facilities, and virtual reality stations for creative and technical projects.</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-center gap-2">
                    <ArrowRight className="h-5 w-5 text-aul-gold" />
                    <span>Study Spaces</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p>Diverse study environments including silent reading rooms, collaborative spaces, and reservable group study rooms with technology.</p>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Library Hours & Services */}
          <div className="grid md:grid-cols-2 gap-8 mb-12">
            <Card>
              <CardHeader>
                <CardTitle>Library Hours</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="font-medium">Monday - Thursday</span>
                    <span>7:00 AM - 12:00 AM</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between">
                    <span className="font-medium">Friday</span>
                    <span>7:00 AM - 10:00 PM</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between">
                    <span className="font-medium">Saturday</span>
                    <span>9:00 AM - 8:00 PM</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between">
                    <span className="font-medium">Sunday</span>
                    <span>12:00 PM - 12:00 AM</span>
                  </div>
                  <Separator />
                  <p className="text-sm text-gray-500 pt-2">
                    * Hours may vary during holidays and exam periods. Check the library website for current information.
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Services</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li className="flex items-start gap-2">
                    <ArrowRight className="h-5 w-5 min-w-5 text-aul-gold mt-0.5" />
                    <span>Interlibrary loan for materials not in our collection</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <ArrowRight className="h-5 w-5 min-w-5 text-aul-gold mt-0.5" />
                    <span>Research data management support</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <ArrowRight className="h-5 w-5 min-w-5 text-aul-gold mt-0.5" />
                    <span>Citation management tools and workshops</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <ArrowRight className="h-5 w-5 min-w-5 text-aul-gold mt-0.5" />
                    <span>Digital scholarship center</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <ArrowRight className="h-5 w-5 min-w-5 text-aul-gold mt-0.5" />
                    <span>Information literacy instruction</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <ArrowRight className="h-5 w-5 min-w-5 text-aul-gold mt-0.5" />
                    <span>Document delivery for faculty and graduate students</span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>

          {/* Library Resources */}
          <div className="mb-12">
            <h2 className="text-2xl font-bold mb-6 text-aul-navy">Online Resources</h2>
            <div className="grid sm:grid-cols-2 md:grid-cols-3 gap-4">
              <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center text-center">
                <BookOpen className="h-8 w-8 mb-2" />
                <span className="font-semibold">E-Book Collection</span>
                <span className="text-sm text-gray-500">Access over 500,000 titles</span>
              </Button>
              
              <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center text-center">
                <Search className="h-8 w-8 mb-2" />
                <span className="font-semibold">Library Catalog</span>
                <span className="text-sm text-gray-500">Search all available resources</span>
              </Button>
              
              <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center text-center">
                <Book className="h-8 w-8 mb-2" />
                <span className="font-semibold">Academic Journals</span>
                <span className="text-sm text-gray-500">Full-text access to thousands of journals</span>
              </Button>
              
              <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center text-center">
                <ArrowRight className="h-8 w-8 mb-2" />
                <span className="font-semibold">Research Guides</span>
                <span className="text-sm text-gray-500">Subject-specific resources</span>
              </Button>
              
              <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center text-center">
                <ArrowRight className="h-8 w-8 mb-2" />
                <span className="font-semibold">Citation Tools</span>
                <span className="text-sm text-gray-500">Manage references and citations</span>
              </Button>
              
              <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center text-center">
                <ArrowRight className="h-8 w-8 mb-2" />
                <span className="font-semibold">Course Reserves</span>
                <span className="text-sm text-gray-500">Access course materials online</span>
              </Button>
            </div>
          </div>

          {/* Contact Information */}
          <div className="bg-gray-100 rounded-lg p-6 text-center">
            <h3 className="text-xl font-bold mb-3">Need Assistance?</h3>
            <p className="mb-4">Our librarians are available to help with your research and information needs</p>
            <div className="flex flex-col md:flex-row gap-4 justify-center">
              <Button>
                Ask a Librarian
              </Button>
              <Button variant="outline">
                Book a Research Consultation
              </Button>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default LibraryPage;
