
import React from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';

const Undergraduate = () => {
  const admissionInfo = [
    {
      title: "Admission Requirements",
      items: [
        "Lebanese Baccalaureate (Official or Equivalent)",
        "French Baccalaureate",
        "International Baccalaureate (IB)",
        "American High School Diploma with SAT scores",
        "British A-Levels or equivalent international certificates",
        "Minimum grade requirements vary by program"
      ]
    },
    {
      title: "Required Documents",
      items: [
        "Completed Admission Application Form (4 pages)",
        "Official High School Transcripts (certified copies)",
        "Official Certificate/Diploma (certified copy)",
        "Copy of Lebanese ID or Passport",
        "4 recent passport-size photographs",
        "Medical Certificate",
        "Birth Certificate (certified copy)",
        "Military Service Certificate (for Lebanese males over 18)"
      ]
    },
    {
      title: "Admission Fees",
      items: [
        "Application Fee: $50 USD (non-refundable)",
        "Language Entrance Exam Fee (if required): $25 USD",
        "Registration Fee: $100 USD (upon acceptance)",
        "Student ID Fee: $10 USD"
      ]
    },
    {
      title: "Language Requirements",
      items: [
        "English Language Entrance Exam (for most programs)",
        "French Language Exam (for French-taught programs)",
        "Arabic Language proficiency may be required",
        "TOEFL/IELTS scores accepted for international students",
        "Exemptions available for students from English-medium schools"
      ]
    }
  ];

  const importantDates = [
    { event: "Application Deadline", date: "August 15, 2024" },
    { event: "Entrance Exams", date: "August 20-25, 2024" },
    { event: "Admission Results", date: "August 30, 2024" },
    { event: "Registration Period", date: "September 1-15, 2024" },
    { event: "Classes Begin", date: "September 16, 2024" }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-4xl font-bold text-aul-navy mb-8">Undergraduate Admissions</h1>

          <div className="mb-8 p-6 bg-aul-gold/10 rounded-lg border border-aul-gold/20">
            <h2 className="text-2xl font-semibold text-aul-navy mb-4">Welcome to AUL</h2>
            <p className="text-gray-700 leading-relaxed">
              Arts, Sciences and Technology University in Lebanon (AUL) welcomes applications from qualified students
              seeking undergraduate education. Our admission process is designed to identify students who will thrive
              in our academic environment and contribute to our diverse university community.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 mb-12">
            {admissionInfo.map((section, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="text-aul-navy">{section.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="list-disc list-inside space-y-2">
                    {section.items.map((item, idx) => (
                      <li key={idx} className="text-gray-600">{item}</li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>

          <Card className="mb-12">
            <CardHeader>
              <CardTitle className="text-aul-navy">Important Dates - Academic Year 2024/2025</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-4">
                {importantDates.map((item, index) => (
                  <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <span className="font-medium text-gray-700">{item.event}</span>
                    <span className="text-aul-navy font-semibold">{item.date}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="mb-12">
            <CardHeader>
              <CardTitle className="text-aul-navy">Application Process</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-8 h-8 bg-aul-navy text-white rounded-full flex items-center justify-center font-semibold">1</div>
                  <div>
                    <h4 className="font-semibold text-gray-800">Submit Application</h4>
                    <p className="text-gray-600">Complete the 4-page admission application form with all required information.</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-8 h-8 bg-aul-navy text-white rounded-full flex items-center justify-center font-semibold">2</div>
                  <div>
                    <h4 className="font-semibold text-gray-800">Submit Documents</h4>
                    <p className="text-gray-600">Provide all required documents including transcripts, certificates, and identification.</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-8 h-8 bg-aul-navy text-white rounded-full flex items-center justify-center font-semibold">3</div>
                  <div>
                    <h4 className="font-semibold text-gray-800">Pay Fees</h4>
                    <p className="text-gray-600">Pay the application fee and language exam fee (if applicable).</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-8 h-8 bg-aul-navy text-white rounded-full flex items-center justify-center font-semibold">4</div>
                  <div>
                    <h4 className="font-semibold text-gray-800">Take Entrance Exams</h4>
                    <p className="text-gray-600">Complete required language entrance exams as scheduled.</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-8 h-8 bg-aul-navy text-white rounded-full flex items-center justify-center font-semibold">5</div>
                  <div>
                    <h4 className="font-semibold text-gray-800">Receive Results</h4>
                    <p className="text-gray-600">Admission decisions will be communicated by the specified date.</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="text-center">
            <Button asChild size="lg" className="mr-4">
              <Link to="/admissions/apply">Apply Now</Link>
            </Button>
            <Button asChild variant="outline" size="lg">
              <Link to="/contact/request-info">Request Information</Link>
            </Button>
          </div>

          <div className="mt-8 p-6 bg-blue-50 rounded-lg">
            <h3 className="text-xl font-semibold text-aul-navy mb-3">Contact Admissions Office</h3>
            <div className="grid md:grid-cols-2 gap-4 text-gray-700">
              <div>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Phone:</strong> +961 9 383 000</p>
              </div>
              <div>
                <p><strong>Office Hours:</strong> Monday - Friday, 8:30 AM - 4:30 PM</p>
                <p><strong>Location:</strong> Main Campus, Kaslik</p>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Undergraduate;
