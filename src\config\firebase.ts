// Firebase configuration for AUL AI Teaching Assistant
import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';
import { getStorage } from 'firebase/storage';

// Firebase configuration object
// Note: Replace these with your actual Firebase project credentials
const firebaseConfig = {
  apiKey: "your-api-key-here",
  authDomain: "aul-ai-assistant.firebaseapp.com",
  projectId: "aul-ai-assistant",
  storageBucket: "aul-ai-assistant.appspot.com",
  messagingSenderId: "123456789",
  appId: "your-app-id-here",
  measurementId: "your-measurement-id-here"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const db = getFirestore(app);
export const auth = getAuth(app);
export const storage = getStorage(app);

// Export the app instance
export default app;

// Firestore collection names
export const COLLECTIONS = {
  USERS: 'users',
  CONVERSATIONS: 'conversations',
  MESSAGES: 'messages',
  COURSES: 'courses',
  KNOWLEDGE_BASE: 'knowledge_base',
  DOCUMENT_CHUNKS: 'document_chunks'
};

// Types for Firestore documents
export interface User {
  id: string;
  email: string;
  name: string;
  studentId?: string;
  enrolledCourses: string[];
  createdAt: Date;
  lastActive: Date;
}

export interface Conversation {
  id: string;
  userId: string;
  courseId?: string;
  title: string;
  createdAt: Date;
  updatedAt: Date;
  messageCount: number;
}

export interface Message {
  id: string;
  conversationId: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  sources?: DocumentSource[];
  metadata?: {
    model?: string;
    tokens?: number;
    processingTime?: number;
  };
}

export interface Course {
  id: string;
  name: string;
  code: string;
  faculty: string;
  description: string;
  instructor: string;
  semester: string;
  year: number;
  isActive: boolean;
  knowledgeBaseIds: string[];
}

export interface KnowledgeBase {
  id: string;
  courseId: string;
  title: string;
  description: string;
  documentType: 'pdf' | 'markdown' | 'text' | 'slides';
  filePath: string;
  uploadedAt: Date;
  processedAt?: Date;
  chunkCount: number;
  isProcessed: boolean;
}

export interface DocumentChunk {
  id: string;
  knowledgeBaseId: string;
  courseId: string;
  content: string;
  chunkIndex: number;
  pageNumber?: number;
  section?: string;
  metadata: {
    source: string;
    title: string;
    chunkSize: number;
    overlap: number;
  };
  embedding?: number[]; // For vector similarity search
  createdAt: Date;
}

export interface DocumentSource {
  title: string;
  page?: number;
  section?: string;
  confidence: number;
}

// Utility functions for Firestore operations
export const createTimestamp = () => new Date();

// Error handling for Firebase operations
export class FirebaseError extends Error {
  constructor(
    message: string,
    public code: string,
    public originalError?: any
  ) {
    super(message);
    this.name = 'FirebaseError';
  }
}

// Helper function to handle Firebase errors
export const handleFirebaseError = (error: any): FirebaseError => {
  console.error('Firebase Error:', error);
  
  switch (error.code) {
    case 'permission-denied':
      return new FirebaseError('Access denied. Please check your permissions.', error.code, error);
    case 'not-found':
      return new FirebaseError('Requested document not found.', error.code, error);
    case 'already-exists':
      return new FirebaseError('Document already exists.', error.code, error);
    case 'resource-exhausted':
      return new FirebaseError('Quota exceeded. Please try again later.', error.code, error);
    case 'unauthenticated':
      return new FirebaseError('Authentication required.', error.code, error);
    default:
      return new FirebaseError(error.message || 'An unknown error occurred.', error.code || 'unknown', error);
  }
};

// Configuration for document processing
export const PROCESSING_CONFIG = {
  MAX_CHUNK_SIZE: 1000, // Maximum characters per chunk
  CHUNK_OVERLAP: 200,   // Overlap between chunks
  MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB max file size
  SUPPORTED_FORMATS: ['pdf', 'txt', 'md', 'docx'],
  EMBEDDING_DIMENSIONS: 768, // For text embeddings
};

// API endpoints configuration
export const API_CONFIG = {
  GEMINI_API_URL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent',
  EMBEDDING_API_URL: 'https://generativelanguage.googleapis.com/v1beta/models/embedding-001:embedContent',
  MAX_TOKENS: 4096,
  TEMPERATURE: 0.7,
  TOP_P: 0.8,
  TOP_K: 40,
};
