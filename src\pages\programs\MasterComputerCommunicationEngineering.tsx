import React from 'react';
import ProgramDetail from '@/components/programs/ProgramDetail';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

const MasterComputerCommunicationEngineering = () => {
  const programData = {
    name: "Master of Engineering in Computer and Communication Engineering",
    description: "The Master's program in Computer and Communication Engineering at AUL is designed for graduates seeking to advance their knowledge and skills in this rapidly evolving field. This advanced program builds on undergraduate foundations to develop specialized expertise in computer systems, networks, and communication technologies. The curriculum emphasizes research, innovation, and advanced technical skills, preparing graduates for leadership roles in industry or further academic pursuits.",
    duration: "2 years (4 semesters)",
    faculty: "Faculty of Engineering",
    degreeAwarded: "Master of Engineering (MEng) in Computer and Communication Engineering",
    majorHighlights: [
      "Advanced research opportunities in specialized areas of computing and communications",
      "State-of-the-art laboratories and research facilities",
      "Industry collaboration on cutting-edge projects",
      "Thesis and non-thesis options available",
      "Opportunities to publish research in international journals and conferences",
      "Small class sizes ensuring personalized attention",
      "Preparation for doctoral studies or leadership roles in industry",
      "Focus on emerging technologies and innovation"
    ],
    careerOpportunities: [
      "Senior Computer Engineer",
      "Research and Development Specialist",
      "Systems Architect",
      "Technical Project Manager",
      "Network Security Consultant",
      "Advanced Systems Designer",
      "University Lecturer",
      "Technology Consultant",
      "Chief Technology Officer",
      "Research Scientist",
      "Telecommunications Director",
      "Advanced Software Engineer",
      "IT Infrastructure Manager",
      "Cybersecurity Manager",
      "PhD Candidate"
    ],
    courseHighlights: [
      {
        year: "First Year - Semester 1",
        courses: [
          "MCCE 601: Advanced Computer Architecture (3 credits)",
          "MCCE 603: Advanced Networking Technologies (3 credits)",
          "MCCE 605: Research Methodology (3 credits)",
          "MCCE 607: Elective I (3 credits)"
        ]
      },
      {
        year: "First Year - Semester 2",
        courses: [
          "MCCE 602: Advanced Software Engineering (3 credits)",
          "MCCE 604: Wireless and Mobile Communications (3 credits)",
          "MCCE 606: Advanced Signal Processing (3 credits)",
          "MCCE 608: Elective II (3 credits)"
        ]
      },
      {
        year: "Second Year - Semester 3",
        courses: [
          "MCCE 701: Information Security and Cryptography (3 credits)",
          "MCCE 703: Cloud Computing and Distributed Systems (3 credits)",
          "MCCE 705: Elective III (3 credits)",
          "MCCE 707: Thesis/Project I (3 credits)"
        ]
      },
      {
        year: "Second Year - Semester 4",
        courses: [
          "MCCE 702: Advanced Topics in Computer Engineering (3 credits)",
          "MCCE 704: Elective IV (3 credits)",
          "MCCE 706: Thesis/Project II (6 credits)"
        ]
      }
    ],
    electiveCourses: [
      "MCCE-E01: Internet of Things and Smart Systems (3 credits)",
      "MCCE-E02: Machine Learning and Pattern Recognition (3 credits)",
      "MCCE-E03: Big Data Analytics (3 credits)",
      "MCCE-E04: Advanced Network Security (3 credits)",
      "MCCE-E05: Embedded Systems Design (3 credits)",
      "MCCE-E06: Advanced Digital Communication (3 credits)",
      "MCCE-E07: Optical Communication Networks (3 credits)",
      "MCCE-E08: Satellite Communication Systems (3 credits)",
      "MCCE-E09: Advanced Database Systems (3 credits)",
      "MCCE-E10: Multimedia Processing and Communications (3 credits)"
    ],
    admissionRequirements: [
      "Bachelor's degree in Computer Engineering, Electrical Engineering, or related field",
      "Minimum GPA of 3.0 on a 4.0 scale or equivalent",
      "Strong background in computing, mathematics, and communications",
      "English language proficiency (TOEFL score of 550+ or equivalent)",
      "Two letters of recommendation",
      "Statement of purpose",
      "Personal interview with the department"
    ],
    programObjectives: [
      "Develop advanced knowledge and skills in computer and communication engineering",
      "Build research capabilities and analytical thinking for complex engineering problems",
      "Master advanced design methodologies for computing and communication systems",
      "Develop expertise in specialized areas of the discipline",
      "Contribute to the advancement of knowledge through research and innovation",
      "Prepare for leadership roles in industry or academia",
      "Foster ethical awareness and professional responsibility in advanced technology",
      "Develop skills for lifelong learning and adaptation to evolving technologies"
    ],
    videoUrl: "https://www.youtube.com/embed/YQUjE2koNRI"
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow">
        <ProgramDetail program={programData} />
      </main>
      <Footer />
    </div>
  );
};

export default MasterComputerCommunicationEngineering;
