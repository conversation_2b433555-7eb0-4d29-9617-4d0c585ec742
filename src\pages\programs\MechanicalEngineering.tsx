
import React from 'react';
import ProgramDetail from '@/components/programs/ProgramDetail';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

const MechanicalEngineering = () => {
  const programData = {
    name: "Bachelor of Engineering in Mechanical Engineering",
    description: "Our Mechanical Engineering program focuses on the design, analysis, and manufacturing of mechanical systems. Students learn to solve complex engineering problems involving motion, energy, and force.",
    duration: "4 years",
    faculty: "Faculty of Engineering",
    degreeAwarded: "Bachelor of Engineering (BEng) in Mechanical Engineering",
    careerOpportunities: [
      "Mechanical Engineer",
      "Design Engineer",
      "Manufacturing Engineer",
      "Thermal Systems Engineer",
      "Automotive Engineer",
      "Aerospace Engineer",
      "Robotics Engineer",
      "Energy Systems Engineer"
    ],
    courseHighlights: [
      {
        year: "First Year",
        courses: [
          "Engineering Mechanics",
          "Materials Science",
          "Calculus for Engineers",
          "Engineering Drawing",
          "Introduction to Mechanical Engineering"
        ]
      },
      {
        year: "Second Year",
        courses: [
          "Thermodynamics",
          "Fluid Mechanics",
          "Mechanics of Materials",
          "Manufacturing Processes",
          "Dynamics"
        ]
      },
      {
        year: "Third Year",
        courses: [
          "Machine Design",
          "Heat Transfer",
          "Control Systems",
          "Vibrations",
          "CAD/CAM"
        ]
      },
      {
        year: "Fourth Year",
        courses: [
          "Robotics",
          "HVAC Systems",
          "Finite Element Analysis",
          "Capstone Design Project",
          "Professional Ethics"
        ]
      }
    ],
    admissionRequirements: [
      "High School Diploma or equivalent",
      "Minimum GPA of 3.0",
      "Strong background in Mathematics and Physics",
      "Problem-solving aptitude",
      "English language proficiency",
      "Statement of purpose"
    ],
    programObjectives: [
      "Develop strong understanding of mechanical engineering principles",
      "Master design and analysis of mechanical systems",
      "Apply engineering knowledge to solve complex mechanical problems",
      "Use modern engineering tools and technologies effectively",
      "Understand professional and ethical responsibilities of engineers",
      "Prepare for careers in design, manufacturing, and energy systems"
    ]
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow">
        <ProgramDetail program={programData} />
      </main>
      <Footer />
    </div>
  );
};

export default MechanicalEngineering;
