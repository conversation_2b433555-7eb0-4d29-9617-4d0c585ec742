import React, { useState, useRef, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  Bot, 
  User, 
  Send, 
  Loader2, 
  FileText, 
  MessageSquare,
  BookOpen,
  ArrowLeft,
  Settings,
  MoreVertical
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useAI } from '@/contexts/AIContext';
import Layout from '@/components/layout/Layout';
import MessageBubble from '@/components/chat/MessageBubble';
import TypingIndicator from '@/components/chat/TypingIndicator';
import SourceCitation from '@/components/chat/SourceCitation';

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  sources?: Array<{
    title: string;
    page?: number;
    section?: string;
    confidence: number;
  }>;
}

const ChatPage = () => {
  const { conversationId } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { sendMessage, isLoading } = useAI();
  
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: "Hello! I'm your AI Teaching Assistant for AUL. I can help you with questions about your courses, assignments, and study materials. What would you like to know?",
      sender: 'ai',
      timestamp: new Date(),
      sources: []
    }
  ]);
  
  const [inputMessage, setInputMessage] = useState('');
  const [selectedCourse, setSelectedCourse] = useState<string>('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // Load conversation if conversationId is provided
    if (conversationId) {
      // TODO: Load conversation from Firebase
      console.log('Loading conversation:', conversationId);
    }
  }, [conversationId]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputMessage.trim(),
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');

    try {
      // Call AI service
      const response = await sendMessage(inputMessage, selectedCourse);
      
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: response.content,
        sender: 'ai',
        timestamp: new Date(),
        sources: response.sources
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('Error sending message:', error);
      
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: "I apologize, but I'm having trouble processing your request right now. Please try again in a moment.",
        sender: 'ai',
        timestamp: new Date(),
        sources: []
      };

      setMessages(prev => [...prev, errorMessage]);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleNewChat = () => {
    setMessages([{
      id: '1',
      content: "Hello! I'm your AI Teaching Assistant for AUL. I can help you with questions about your courses, assignments, and study materials. What would you like to know?",
      sender: 'ai',
      timestamp: new Date(),
      sources: []
    }]);
    navigate('/chat');
  };

  return (
    <Layout>
      <div className="flex h-full">
        {/* Sidebar */}
        <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
          {/* Header */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">AI Assistant</h2>
              <button
                onClick={handleNewChat}
                className="bg-blue-600 text-white px-3 py-1.5 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
              >
                New Chat
              </button>
            </div>
            
            {/* Course Selection */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Course Context</label>
              <select
                value={selectedCourse}
                onChange={(e) => setSelectedCourse(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">General Questions</option>
                <option value="cs101">CS 101 - Intro to Computer Science</option>
                <option value="eng201">ENG 201 - Engineering Mathematics</option>
                <option value="bus301">BUS 301 - Business Management</option>
                <option value="arts101">ARTS 101 - Communication Arts</option>
              </select>
            </div>
          </div>

          {/* Chat History */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-2">
              <div className="text-sm font-medium text-gray-700 mb-3">Recent Conversations</div>
              {/* TODO: Load from Firebase */}
              <div className="p-3 rounded-lg hover:bg-gray-50 cursor-pointer border border-gray-200">
                <div className="font-medium text-sm text-gray-900">Mathematics Help</div>
                <div className="text-xs text-gray-500 mt-1">2 hours ago</div>
              </div>
              <div className="p-3 rounded-lg hover:bg-gray-50 cursor-pointer">
                <div className="font-medium text-sm text-gray-900">Programming Assignment</div>
                <div className="text-xs text-gray-500 mt-1">Yesterday</div>
              </div>
            </div>
          </div>

          {/* User Info */}
          <div className="p-4 border-t border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                <User className="w-4 h-4 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium text-gray-900 truncate">
                  {user?.displayName || user?.email}
                </div>
                <div className="text-xs text-gray-500">AUL Student</div>
              </div>
              <button className="text-gray-400 hover:text-gray-600">
                <Settings className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Main Chat Area */}
        <div className="flex-1 flex flex-col">
          {/* Chat Header */}
          <div className="bg-white border-b border-gray-200 px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full flex items-center justify-center">
                  <Bot className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-lg font-semibold text-gray-900">AI Teaching Assistant</h1>
                  <p className="text-sm text-gray-500">
                    {selectedCourse ? `Helping with ${selectedCourse}` : 'Ready to help with any course'}
                  </p>
                </div>
              </div>
              <button className="text-gray-400 hover:text-gray-600">
                <MoreVertical className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Messages Area */}
          <div className="flex-1 overflow-y-auto bg-gray-50 p-6">
            <div className="max-w-4xl mx-auto space-y-6">
              {messages.map((message) => (
                <div key={message.id}>
                  <MessageBubble message={message} />
                  {message.sources && message.sources.length > 0 && (
                    <div className="mt-2">
                      <SourceCitation sources={message.sources} />
                    </div>
                  )}
                </div>
              ))}
              
              {isLoading && <TypingIndicator />}
              <div ref={messagesEndRef} />
            </div>
          </div>

          {/* Input Area */}
          <div className="bg-white border-t border-gray-200 p-6">
            <div className="max-w-4xl mx-auto">
              <div className="flex items-end space-x-4">
                <div className="flex-1">
                  <textarea
                    ref={inputRef}
                    value={inputMessage}
                    onChange={(e) => setInputMessage(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Ask a question about your course..."
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                    rows={1}
                    style={{ minHeight: '48px', maxHeight: '120px' }}
                    disabled={isLoading}
                  />
                </div>
                <button
                  onClick={handleSendMessage}
                  disabled={!inputMessage.trim() || isLoading}
                  className="bg-blue-600 text-white p-3 rounded-xl hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isLoading ? (
                    <Loader2 className="w-5 h-5 animate-spin" />
                  ) : (
                    <Send className="w-5 h-5" />
                  )}
                </button>
              </div>
              
              <div className="flex items-center justify-between mt-3 text-xs text-gray-500">
                <div>Press Enter to send, Shift+Enter for new line</div>
                <div className="flex items-center space-x-4">
                  <span className="flex items-center space-x-1">
                    <FileText className="w-3 h-3" />
                    <span>Sources included</span>
                  </span>
                  <span className="flex items-center space-x-1">
                    <MessageSquare className="w-3 h-3" />
                    <span>Context aware</span>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default ChatPage;
