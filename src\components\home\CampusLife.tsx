import React from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';

const CampusLife = () => {
  return (
    <section className="section-padding bg-white overflow-hidden">
      <div className="aul-container">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-aul-navy mb-4">Campus Life</h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            At AUL, we believe that education extends beyond the classroom. Discover the vibrant and diverse
            student experience that makes our university community special.
          </p>
        </div>

        {/* Grid layout for campus life highlights */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Student Clubs & Activities */}
          <div className="relative group overflow-hidden rounded-lg h-80">
            <img
              src="/images/home/<USER>"
              alt="Student Clubs & Activities"
              className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-aul-navy via-aul-navy/50 to-transparent opacity-90"></div>
            <div className="absolute bottom-0 left-0 p-6 text-white">
              <h3 className="text-xl font-bold mb-2">Student Clubs & Activities</h3>
              <p className="mb-4">Join one of our 50+ student clubs and organizations to pursue your interests.</p>
              <Link
                to="/student-life/clubs"
                className="text-aul-navy bg-aul-gold hover:bg-aul-gold/90 transition-colors py-2 px-4 rounded inline-block font-semibold"
              >
                Explore Clubs
              </Link>
            </div>
          </div>

          {/* Athletics & Recreation */}
          <div className="relative group overflow-hidden rounded-lg h-80">
            <img
              src="/images/Athletics & Recreation/IMG_3119.JPG"
              alt="Athletics & Recreation"
              className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-aul-navy via-aul-navy/50 to-transparent opacity-90"></div>
            <div className="absolute bottom-0 left-0 p-6 text-white">
              <h3 className="text-xl font-bold mb-2">Athletics & Recreation</h3>
              <p className="mb-4">Stay active with our competitive sports teams and recreational facilities.</p>
              <Link
                to="/student-life/athletics"
                className="text-aul-navy bg-aul-gold hover:bg-aul-gold/90 transition-colors py-2 px-4 rounded inline-block font-semibold"
              >
                View Sports
              </Link>
            </div>
          </div>

          {/* AUL Campuses */}
          <div className="relative group overflow-hidden rounded-lg h-80">
            <img
              src="/images/home/<USER>"
              alt="AUL Campuses"
              className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-aul-navy via-aul-navy/50 to-transparent opacity-90"></div>
            <div className="absolute bottom-0 left-0 p-6 text-white">
              <h3 className="text-xl font-bold mb-2">AUL Campuses</h3>
              <p className="mb-4">Discover our 6 campuses across Lebanon, each offering unique facilities and programs.</p>
              <Link
                to="/about/campus-locations"
                className="text-aul-navy bg-aul-gold hover:bg-aul-gold/90 transition-colors py-2 px-4 rounded inline-block font-semibold"
              >
                Explore Campuses
              </Link>
            </div>
          </div>
        </div>

        {/* Video Section */}
        <div className="mt-16 bg-gradient-to-r from-aul-gray to-white rounded-xl overflow-hidden shadow-lg">
          <div className="grid grid-cols-1 lg:grid-cols-2">
            <div className="p-8 lg:p-12 flex flex-col justify-center">
              <h3 className="text-2xl md:text-3xl font-bold text-aul-navy mb-4">Experience AUL</h3>
              <p className="text-lg text-gray-600 mb-6">
                Take a virtual tour of our campus and discover what makes AUL a special place to learn,
                grow, and thrive. From state-of-the-art facilities to vibrant community spaces,
                explore all that our university has to offer.
              </p>
              <div className="flex flex-wrap gap-4">
                <Button className="bg-aul-blue hover:bg-aul-navy" asChild>
                  <Link to="/virtual-tour">Virtual Tour</Link>
                </Button>
                <Button variant="outline" className="border-aul-blue text-aul-blue hover:bg-aul-blue hover:text-white" asChild>
                  <Link to="/campus-map">Campus Map</Link>
                </Button>
              </div>
            </div>
            <div className="w-full aspect-video rounded-lg overflow-hidden shadow-lg bg-gray-100">
              <video
                className="w-full h-full object-cover"
                controls
                autoPlay
                muted
                loop
                playsInline
                preload="metadata"
                onError={(e) => {
                  console.error('Video error:', e);
                  const video = e.target as HTMLVideoElement;
                  video.style.display = 'none';
                  const errorMessage = document.createElement('div');
                  errorMessage.className = 'w-full h-full flex items-center justify-center text-red-500';
                  errorMessage.textContent = 'Error loading video. Please try refreshing the page.';
                  video.parentNode?.appendChild(errorMessage);
                }}
              >
                <source src="/videos/aul1.mp4" type="video/mp4" />
                <source src="/videos/aul1.mp4" type="video/quicktime" />
                Your browser does not support the video tag.
              </video>
            </div>
          </div>
        </div>

        <div className="mt-12 text-center">
          <Button variant="default" asChild>
            <Link to="/student-life">Explore Student Life</Link>
          </Button>
        </div>
      </div>
    </section>
  );
};

export default CampusLife;
