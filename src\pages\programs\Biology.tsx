
import React from 'react';
import ProgramDetail from '@/components/programs/ProgramDetail';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

const Biology = () => {
  const programData = {
    name: "Bachelor of Science in Biology",
    description: "The Biology program at AUL provides students with a comprehensive understanding of living organisms, from molecular and cellular levels to ecosystems. Our curriculum combines theoretical knowledge with extensive hands-on laboratory work and field studies, preparing graduates for diverse careers in biological sciences. Students develop strong research skills through investigative projects and gain expertise in modern biological techniques and applications.",
    duration: "3 years (6 semesters)",
    faculty: "Faculty of Sciences",
    degreeAwarded: "Bachelor of Science (BSc) in Biology",
    majorHighlights: [
      "State-of-the-art biology laboratories with modern equipment",
      "Extensive hands-on practical experience in all biology disciplines",
      "Field trips to diverse ecosystems for environmental studies",
      "Research opportunities with faculty mentors",
      "Small class sizes ensuring personalized attention",
      "Interdisciplinary approach connecting biology with other sciences",
      "Focus on both theoretical foundations and practical applications",
      "Preparation for graduate studies and professional certifications"
    ],
    careerOpportunities: [
      "Research Scientist",
      "Laboratory Technician",
      "Environmental Consultant",
      "Healthcare Professional",
      "Biotechnology Specialist",
      "Conservation Biologist",
      "Science Educator",
      "Pharmaceutical Researcher",
      "Food Science Technologist",
      "Microbiologist",
      "Genetic Counselor",
      "Ecologist",
      "Marine Biologist",
      "Quality Control Analyst",
      "Scientific Writer"
    ],
    courseHighlights: [
      {
        year: "First Year - Semester 1",
        courses: [
          "BIOL 201: General Biology I (3 credits)",
          "CHEM 201: General Chemistry I (3 credits)",
          "MATH 201: Calculus for Life Sciences (3 credits)",
          "ENGL 201: English Communication Skills I (3 credits)",
          "ARAB 201: Arabic Essay Reading and Writing I (2 credits)"
        ]
      },
      {
        year: "First Year - Semester 2",
        courses: [
          "BIOL 202: General Biology II (3 credits)",
          "BIOL 204: Cell Biology (3 credits)",
          "CHEM 202: General Chemistry II (3 credits)",
          "PHYS 201: Physics for Life Sciences (3 credits)",
          "ENGL 202: English Communication Skills II (3 credits)"
        ]
      },
      {
        year: "Second Year - Semester 3",
        courses: [
          "BIOL 301: Genetics (3 credits)",
          "BIOL 303: Microbiology (3 credits)",
          "BIOL 305: Ecology (3 credits)",
          "CHEM 301: Organic Chemistry (3 credits)",
          "COMP 201: Computer Applications in Biology (2 credits)"
        ]
      },
      {
        year: "Second Year - Semester 4",
        courses: [
          "BIOL 302: Molecular Biology (3 credits)",
          "BIOL 304: Human Physiology (3 credits)",
          "BIOL 306: Evolutionary Biology (3 credits)",
          "BIOL 308: Biostatistics (3 credits)",
          "BIOL 310: Scientific Writing (2 credits)"
        ]
      },
      {
        year: "Third Year - Semester 5",
        courses: [
          "BIOL 401: Biochemistry (3 credits)",
          "BIOL 403: Plant Biology (3 credits)",
          "BIOL 405: Animal Physiology (3 credits)",
          "BIOL 407: Genomics and Proteomics (3 credits)",
          "BIOL 409: Research Methods in Biology (3 credits)"
        ]
      },
      {
        year: "Third Year - Semester 6",
        courses: [
          "BIOL 402: Immunology (3 credits)",
          "BIOL 404: Conservation Biology (3 credits)",
          "BIOL 406: Biotechnology Applications (3 credits)",
          "BIOL 408: Bioethics (2 credits)",
          "BIOL 410: Senior Research Project (4 credits)"
        ]
      }
    ],
    admissionRequirements: [
      "Lebanese Baccalaureate (Life Sciences or General Sciences) or equivalent",
      "Minimum overall average of 12/20 or equivalent",
      "Strong background in Biology and Chemistry",
      "Laboratory experience (preferred)",
      "English language proficiency (TOEFL score of 500+ or equivalent)",
      "Personal interview with the department"
    ],
    programObjectives: [
      "Develop deep understanding of biological principles and processes",
      "Master laboratory techniques and research methods in biological sciences",
      "Analyze biological data using statistical and computational tools",
      "Understand the interconnections between organisms and their environments",
      "Develop critical thinking and problem-solving skills for biological research",
      "Apply biological knowledge to address real-world challenges",
      "Prepare for careers in research, healthcare, and biotechnology",
      "Foster ethical awareness in biological research and applications"
    ],
    videoUrl: "https://www.youtube.com/embed/QQezL9pLVcI"
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow">
        <ProgramDetail program={programData} />
      </main>
      <Footer />
    </div>
  );
};

export default Biology;
