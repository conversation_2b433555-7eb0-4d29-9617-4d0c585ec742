
import React from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { MapPin, Phone, Mail, Clock, Users, Building } from 'lucide-react';

const CampusLocations = () => {
  const locations = [
    {
      name: "Beirut Campus",
      address: "University Centre, Sin El Fil, Beirut",
      phone: "+961 (01) 49 29 11 / +961 (01) 49 41 05",
      fax: "+961 1 708301",
      description: "Our main campus in the heart of Beirut, offering comprehensive academic programs and state-of-the-art facilities.",
      image: "/images/home/<USER>",
      features: [
        "Main administrative offices",
        "Central library and learning resources",
        "Modern lecture halls and classrooms",
        "Student services center",
        "Computer and science laboratories",
        "Faculty offices and research facilities"
      ],
      programs: [
        "All undergraduate programs",
        "Graduate programs",
        "Continuing education",
        "Professional development courses"
      ]
    },
    {
      name: "Jadra Campus",
      address: "Jadra, Lebanon",
      phone: "+961 (01) 49 29 11",
      description: "A modern campus providing quality education in a serene environment, equipped with contemporary facilities.",
      image: "/images/home/<USER>",
      features: [
        "Modern academic buildings",
        "Specialized laboratories",
        "Student recreation areas",
        "Parking facilities",
        "Cafeteria and dining services",
        "Sports and fitness facilities"
      ],
      programs: [
        "Business Administration",
        "Engineering programs",
        "Computer Science",
        "Arts and Sciences"
      ]
    },
    {
      name: "Dekwaneh Campus",
      address: "Dekwaneh, Lebanon",
      phone: "+961 (01) 49 29 11",
      description: "Strategically located campus offering accessible education with modern infrastructure and dedicated faculty.",
      image: "/images/home/<USER>",
      features: [
        "Contemporary classrooms",
        "Technology-enhanced learning spaces",
        "Student support services",
        "Library and study areas",
        "Faculty research offices",
        "Conference and meeting rooms"
      ],
      programs: [
        "Business and Management",
        "Information Technology",
        "Arts and Humanities",
        "Foundation programs"
      ]
    },
    {
      name: "Sin El Fil Campus",
      address: "Sin El Fil, Lebanon",
      phone: "+961 (01) 49 29 11",
      description: "A vibrant campus community fostering academic excellence and student engagement in a modern setting.",
      image: "/images/home/<USER>",
      features: [
        "Interactive learning environments",
        "Student activity centers",
        "Modern laboratory facilities",
        "Academic support services",
        "Collaborative study spaces",
        "Administrative services"
      ],
      programs: [
        "Sciences and Mathematics",
        "Engineering disciplines",
        "Business studies",
        "Liberal arts programs"
      ]
    },
    {
      name: "Kaslik Campus",
      address: "Kaslik, Lebanon",
      phone: "+961 (01) 49 29 11",
      description: "Serving the coastal region with quality higher education and comprehensive student services.",
      image: "/images/home/<USER>",
      features: [
        "Specialized engineering labs",
        "Research and development centers",
        "Student housing assistance",
        "Career guidance services",
        "Industry partnership programs",
        "Innovation hubs"
      ],
      programs: [
        "Engineering and Technology",
        "Applied Sciences",
        "Business and Economics",
        "Professional certifications"
      ]
    },
    {
      name: "Tripoli Campus",
      address: "Tripoli, Lebanon",
      phone: "+961 (01) 49 29 11",
      description: "Bringing quality higher education to Northern Lebanon with comprehensive academic offerings.",
      image: "/images/home/<USER>",
      features: [
        "Regional academic center",
        "Community outreach programs",
        "Professional training facilities",
        "Local industry partnerships",
        "Student mentorship programs",
        "Cultural and social activities"
      ],
      programs: [
        "Regional development programs",
        "Business and entrepreneurship",
        "Technical education",
        "Community service learning"
      ]
    },
    {
      name: "Chtaura Campus",
      address: "Chtaura, Bekaa Valley, Lebanon",
      phone: "+961 (01) 49 29 11",
      description: "Serving the Bekaa Valley region with accessible higher education and agricultural research programs.",
      image: "/images/home/<USER>",
      features: [
        "Agricultural research facilities",
        "Environmental studies labs",
        "Rural development programs",
        "Community extension services",
        "Sustainable agriculture initiatives",
        "Regional cultural preservation"
      ],
      programs: [
        "Agricultural sciences",
        "Environmental studies",
        "Rural development",
        "Sustainable business practices"
      ]
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow">
        {/* Hero Section */}
        <section className="relative h-[60vh] min-h-[400px] flex items-center overflow-hidden">
          <div className="absolute inset-0 z-0">
            <img
              src="/images/home/<USER>"
              alt="AUL Campuses"
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-black/50"></div>
          </div>
          <div className="aul-container relative z-10 text-white">
            <div className="max-w-4xl">
              <h1 className="text-5xl md:text-6xl font-bold mb-6">Our Campuses</h1>
              <p className="text-xl md:text-2xl mb-8">
                AUL operates across 7 strategic locations throughout Lebanon, bringing quality higher education closer to students nationwide.
              </p>
              <div className="flex items-center gap-6 text-lg">
                <div className="flex items-center gap-2">
                  <Building className="w-6 h-6 text-aul-gold" />
                  <span>7 Campuses</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="w-6 h-6 text-aul-gold" />
                  <span>2000+ Students</span>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="w-6 h-6 text-aul-gold" />
                  <span>Nationwide Coverage</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Campus Overview */}
        <section className="py-16 bg-white">
          <div className="aul-container">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-aul-navy mb-4">
                Excellence Across Lebanon
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                From the bustling capital of Beirut to the serene Bekaa Valley, our campuses are strategically
                located to serve students from all regions of Lebanon. Each campus maintains our high standards
                of academic excellence while adapting to local community needs.
              </p>
            </div>

            {/* Campus Grid */}
            <div className="space-y-12">
              {locations.map((location, index) => (
                <Card key={index} className="overflow-hidden hover:shadow-xl transition-all duration-300">
                  <div className={`grid lg:grid-cols-2 gap-0 ${index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''}`}>
                    {/* Image Section */}
                    <div className={`relative h-80 lg:h-auto ${index % 2 === 1 ? 'lg:order-2' : ''}`}>
                      <img
                        src={location.image}
                        alt={location.name}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                    </div>

                    {/* Content Section */}
                    <div className={`p-8 lg:p-12 ${index % 2 === 1 ? 'lg:order-1' : ''}`}>
                      <CardHeader className="p-0 mb-6">
                        <CardTitle className="text-2xl md:text-3xl text-aul-navy mb-2">
                          {location.name}
                        </CardTitle>
                        <div className="flex items-start gap-2 text-gray-600 mb-4">
                          <MapPin className="w-5 h-5 text-aul-gold mt-0.5 flex-shrink-0" />
                          <span>{location.address}</span>
                        </div>
                        {location.phone && (
                          <div className="flex items-center gap-2 text-gray-600 mb-4">
                            <Phone className="w-5 h-5 text-aul-gold" />
                            <span>{location.phone}</span>
                          </div>
                        )}
                        <p className="text-gray-700 leading-relaxed">
                          {location.description}
                        </p>
                      </CardHeader>

                      <CardContent className="p-0">
                        <div className="grid md:grid-cols-2 gap-6">
                          {/* Features */}
                          <div>
                            <h4 className="font-semibold text-aul-navy mb-3 flex items-center gap-2">
                              <Building className="w-5 h-5 text-aul-gold" />
                              Campus Features
                            </h4>
                            <ul className="space-y-2">
                              {location.features.slice(0, 4).map((feature, idx) => (
                                <li key={idx} className="flex items-start gap-2 text-gray-600">
                                  <span className="w-2 h-2 bg-aul-gold rounded-full mt-2 flex-shrink-0"></span>
                                  <span className="text-sm">{feature}</span>
                                </li>
                              ))}
                            </ul>
                          </div>

                          {/* Programs */}
                          <div>
                            <h4 className="font-semibold text-aul-navy mb-3 flex items-center gap-2">
                              <Users className="w-5 h-5 text-aul-gold" />
                              Academic Programs
                            </h4>
                            <ul className="space-y-2">
                              {location.programs.map((program, idx) => (
                                <li key={idx} className="flex items-start gap-2 text-gray-600">
                                  <span className="w-2 h-2 bg-aul-blue rounded-full mt-2 flex-shrink-0"></span>
                                  <span className="text-sm">{program}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>

                        <div className="mt-6 flex flex-wrap gap-3">
                          <Button className="bg-aul-navy hover:bg-aul-blue">
                            Visit Campus
                          </Button>
                          <Button variant="outline" className="border-aul-gold text-aul-navy hover:bg-aul-gold hover:text-white">
                            Get Directions
                          </Button>
                        </div>
                      </CardContent>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Contact Section */}
        <section className="py-16 bg-aul-gray">
          <div className="aul-container">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-aul-navy mb-4">
                Visit Our Campuses
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                We welcome prospective students and their families to visit our campuses.
                Schedule a tour to experience our facilities and meet our faculty.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              <Card className="text-center p-6">
                <div className="w-16 h-16 bg-aul-navy rounded-full flex items-center justify-center mx-auto mb-4">
                  <Phone className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-aul-navy mb-2">Call Us</h3>
                <p className="text-gray-600 mb-4">Speak with our admissions team</p>
                <p className="font-semibold text-aul-navy">+961 (01) 49 29 11</p>
              </Card>

              <Card className="text-center p-6">
                <div className="w-16 h-16 bg-aul-gold rounded-full flex items-center justify-center mx-auto mb-4">
                  <Mail className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-aul-navy mb-2">Email Us</h3>
                <p className="text-gray-600 mb-4">Get detailed information</p>
                <p className="font-semibold text-aul-navy"><EMAIL></p>
              </Card>

              <Card className="text-center p-6">
                <div className="w-16 h-16 bg-aul-blue rounded-full flex items-center justify-center mx-auto mb-4">
                  <Clock className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-aul-navy mb-2">Visit Hours</h3>
                <p className="text-gray-600 mb-4">Monday to Friday</p>
                <p className="font-semibold text-aul-navy">8:00 AM - 5:00 PM</p>
              </Card>
            </div>

            <div className="text-center mt-12">
              <Button size="lg" className="bg-aul-navy hover:bg-aul-blue text-lg px-8 py-3">
                Schedule a Campus Tour
              </Button>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default CampusLocations;
