import React from 'react';
import ProgramDetail from '@/components/programs/ProgramDetail';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

const ComputerCommunicationEngineering = () => {
  const programData = {
    name: "Bachelor of Engineering in Computer and Communication Engineering",
    description: "The Computer and Communication Engineering program at AUL provides students with a comprehensive education in the design, development, and implementation of computer systems and communication networks. Our curriculum combines theoretical knowledge with practical applications, preparing graduates for successful careers in this rapidly evolving field. Students gain expertise in hardware design, software development, telecommunications, and network security.",
    duration: "4 years (8 semesters)",
    faculty: "Faculty of Engineering",
    degreeAwarded: "Bachelor of Engineering (BEng) in Computer and Communication Engineering",
    majorHighlights: [
      "Advanced computer and networking laboratories with industry-standard equipment",
      "Comprehensive curriculum covering both hardware and software aspects",
      "Focus on emerging technologies including IoT, AI, and cloud computing",
      "Hands-on experience with real-world engineering projects",
      "Industry partnerships providing internship opportunities",
      "Specialized training in cybersecurity and data protection",
      "Capstone projects addressing contemporary technological challenges",
      "Preparation for professional certifications in networking and computing"
    ],
    careerOpportunities: [
      "Computer Engineer",
      "Network Engineer",
      "Telecommunications Engineer",
      "Systems Engineer",
      "Hardware Designer",
      "Software Developer",
      "Cybersecurity Specialist",
      "IoT Solutions Architect",
      "Network Administrator",
      "Cloud Infrastructure Engineer",
      "Mobile Application Developer",
      "Embedded Systems Engineer",
      "Technical Consultant",
      "Research and Development Engineer",
      "IT Project Manager"
    ],
    courseHighlights: [
      {
        year: "First Year - Semester 1",
        courses: [
          "CCE 201: Introduction to Computer Engineering (3 credits)",
          "ENGR 201: Engineering Mechanics (3 credits)",
          "MATH 211: Calculus I (3 credits)",
          "PHYS 201: Physics for Engineers I (3 credits)",
          "ENGL 201: English Communication Skills I (3 credits)"
        ]
      },
      {
        year: "First Year - Semester 2",
        courses: [
          "CCE 202: Digital Logic Design (3 credits)",
          "CCE 204: Programming Fundamentals (3 credits)",
          "MATH 212: Calculus II (3 credits)",
          "PHYS 202: Physics for Engineers II (3 credits)",
          "ENGL 202: English Communication Skills II (3 credits)"
        ]
      },
      {
        year: "Second Year - Semester 3",
        courses: [
          "CCE 301: Data Structures and Algorithms (3 credits)",
          "CCE 303: Computer Architecture (3 credits)",
          "CCE 305: Electronic Circuits (3 credits)",
          "MATH 311: Differential Equations (3 credits)",
          "ARAB 201: Arabic Essay Reading and Writing (2 credits)"
        ]
      },
      {
        year: "Second Year - Semester 4",
        courses: [
          "CCE 302: Object-Oriented Programming (3 credits)",
          "CCE 304: Signals and Systems (3 credits)",
          "CCE 306: Database Systems (3 credits)",
          "MATH 312: Numerical Analysis (3 credits)",
          "ENGL 301: Technical Writing (2 credits)"
        ]
      },
      {
        year: "Third Year - Semester 5",
        courses: [
          "CCE 401: Operating Systems (3 credits)",
          "CCE 403: Data Communication and Networks (3 credits)",
          "CCE 405: Microprocessors and Embedded Systems (3 credits)",
          "CCE 407: Digital Signal Processing (3 credits)",
          "ENGR 401: Engineering Economics (3 credits)"
        ]
      },
      {
        year: "Third Year - Semester 6",
        courses: [
          "CCE 402: Computer Networks (3 credits)",
          "CCE 404: Software Engineering (3 credits)",
          "CCE 406: Wireless Communication Systems (3 credits)",
          "CCE 408: Web Application Development (3 credits)",
          "CCE 410: Summer Training (0 credits)"
        ]
      },
      {
        year: "Fourth Year - Semester 7",
        courses: [
          "CCE 501: Network Security (3 credits)",
          "CCE 503: Mobile Application Development (3 credits)",
          "CCE 505: Cloud Computing (3 credits)",
          "CCE 507: Engineering Ethics & Professionalism (2 credits)",
          "CCE 509: Capstone Design Project I (2 credits)"
        ]
      },
      {
        year: "Fourth Year - Semester 8",
        courses: [
          "CCE 502: Internet of Things (3 credits)",
          "CCE 504: Artificial Intelligence (3 credits)",
          "CCE 506: Technical Elective (3 credits)",
          "CCE 508: Entrepreneurship for Engineers (2 credits)",
          "CCE 510: Capstone Design Project II (3 credits)"
        ]
      }
    ],
    admissionRequirements: [
      "Lebanese Baccalaureate (Scientific Stream) or equivalent",
      "Minimum overall average of 12/20 or equivalent",
      "Strong background in Mathematics and Physics",
      "Basic computer literacy",
      "English language proficiency (TOEFL score of 500+ or equivalent)",
      "Personal interview with the department"
    ],
    programObjectives: [
      "Develop strong problem-solving and analytical skills for computing and communication challenges",
      "Master fundamental principles of computer hardware and software design",
      "Build expertise in network design, implementation, and security",
      "Apply engineering knowledge to create innovative technological solutions",
      "Use modern engineering tools and programming languages effectively",
      "Understand professional and ethical responsibilities in the digital age",
      "Develop effective communication and teamwork skills",
      "Prepare for successful careers in the rapidly evolving technology sector"
    ],
    videoUrl: "https://www.youtube.com/embed/KEiH1EkQYyc"
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow">
        <ProgramDetail program={programData} />
      </main>
      <Footer />
    </div>
  );
};

export default ComputerCommunicationEngineering;
