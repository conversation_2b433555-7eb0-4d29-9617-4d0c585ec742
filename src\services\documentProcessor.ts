// Document Processing Service for AUL AI Teaching Assistant
// Handles ingestion and processing of course materials

import { 
  collection, 
  addDoc, 
  updateDoc, 
  doc, 
  writeBatch,
  getDocs,
  query,
  where
} from 'firebase/firestore';
import { 
  ref, 
  uploadBytes, 
  getDownloadURL, 
  deleteObject 
} from 'firebase/storage';
import { 
  db, 
  storage, 
  COLLECTIONS, 
  KnowledgeBase, 
  DocumentChunk, 
  PROCESSING_CONFIG,
  handleFirebaseError 
} from '@/config/firebase';

// Types for document processing
export interface ProcessingResult {
  success: boolean;
  knowledgeBaseId?: string;
  chunksCreated: number;
  errors: string[];
  processingTime: number;
}

export interface DocumentMetadata {
  title: string;
  courseId: string;
  description?: string;
  author?: string;
  subject?: string;
}

export interface TextChunk {
  content: string;
  chunkIndex: number;
  pageNumber?: number;
  section?: string;
  metadata: {
    source: string;
    title: string;
    chunkSize: number;
    overlap: number;
  };
}

export class DocumentProcessor {
  
  /**
   * Main method to process and store a document
   */
  async processDocument(
    file: File,
    metadata: DocumentMetadata
  ): Promise<ProcessingResult> {
    const startTime = Date.now();
    const errors: string[] = [];
    let knowledgeBaseId: string | undefined;
    let chunksCreated = 0;

    try {
      // Step 1: Validate file
      const validationResult = this.validateFile(file);
      if (!validationResult.isValid) {
        return {
          success: false,
          chunksCreated: 0,
          errors: validationResult.errors,
          processingTime: Date.now() - startTime
        };
      }

      // Step 2: Upload file to Firebase Storage
      const filePath = await this.uploadFile(file, metadata.courseId);

      // Step 3: Create knowledge base entry
      knowledgeBaseId = await this.createKnowledgeBaseEntry(
        file,
        metadata,
        filePath
      );

      // Step 4: Extract text from file
      const extractedText = await this.extractTextFromFile(file);

      // Step 5: Split text into chunks
      const chunks = this.splitTextIntoChunks(
        extractedText,
        metadata.title,
        knowledgeBaseId
      );

      // Step 6: Store chunks in Firestore
      chunksCreated = await this.storeChunks(chunks, metadata.courseId, knowledgeBaseId);

      // Step 7: Update knowledge base as processed
      await this.markAsProcessed(knowledgeBaseId, chunksCreated);

      return {
        success: true,
        knowledgeBaseId,
        chunksCreated,
        errors,
        processingTime: Date.now() - startTime
      };

    } catch (error) {
      console.error('Document processing error:', error);
      errors.push(`Processing failed: ${error.message}`);

      // Cleanup on failure
      if (knowledgeBaseId) {
        await this.cleanupFailedProcessing(knowledgeBaseId);
      }

      return {
        success: false,
        knowledgeBaseId,
        chunksCreated,
        errors,
        processingTime: Date.now() - startTime
      };
    }
  }

  /**
   * Validate uploaded file
   */
  private validateFile(file: File): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check file size
    if (file.size > PROCESSING_CONFIG.MAX_FILE_SIZE) {
      errors.push(`File size exceeds ${PROCESSING_CONFIG.MAX_FILE_SIZE / (1024 * 1024)}MB limit`);
    }

    // Check file type
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    if (!fileExtension || !PROCESSING_CONFIG.SUPPORTED_FORMATS.includes(fileExtension)) {
      errors.push(`Unsupported file format. Supported formats: ${PROCESSING_CONFIG.SUPPORTED_FORMATS.join(', ')}`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Upload file to Firebase Storage
   */
  private async uploadFile(file: File, courseId: string): Promise<string> {
    const timestamp = Date.now();
    const fileName = `${timestamp}_${file.name}`;
    const filePath = `courses/${courseId}/documents/${fileName}`;
    
    const storageRef = ref(storage, filePath);
    await uploadBytes(storageRef, file);
    
    return filePath;
  }

  /**
   * Create knowledge base entry in Firestore
   */
  private async createKnowledgeBaseEntry(
    file: File,
    metadata: DocumentMetadata,
    filePath: string
  ): Promise<string> {
    const fileExtension = file.name.split('.').pop()?.toLowerCase() as any;
    
    const knowledgeBaseData: Omit<KnowledgeBase, 'id'> = {
      courseId: metadata.courseId,
      title: metadata.title,
      description: metadata.description || '',
      documentType: fileExtension,
      filePath,
      uploadedAt: new Date(),
      chunkCount: 0,
      isProcessed: false
    };

    const docRef = await addDoc(collection(db, COLLECTIONS.KNOWLEDGE_BASE), knowledgeBaseData);
    return docRef.id;
  }

  /**
   * Extract text from different file types
   */
  private async extractTextFromFile(file: File): Promise<string> {
    const fileExtension = file.name.split('.').pop()?.toLowerCase();

    switch (fileExtension) {
      case 'txt':
      case 'md':
        return await this.extractTextFromTextFile(file);
      case 'pdf':
        return await this.extractTextFromPDF(file);
      case 'docx':
        return await this.extractTextFromDocx(file);
      default:
        throw new Error(`Unsupported file type: ${fileExtension}`);
    }
  }

  /**
   * Extract text from plain text files
   */
  private async extractTextFromTextFile(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.onerror = (e) => reject(new Error('Failed to read text file'));
      reader.readAsText(file);
    });
  }

  /**
   * Extract text from PDF (placeholder - requires PDF.js or similar)
   */
  private async extractTextFromPDF(file: File): Promise<string> {
    // This is a placeholder implementation
    // In production, you would use PDF.js or a server-side PDF processing service
    console.warn('PDF processing not implemented - using placeholder text');
    return `[PDF Content from ${file.name}]\nThis is placeholder text. In production, implement PDF text extraction using PDF.js or similar library.`;
  }

  /**
   * Extract text from DOCX (placeholder - requires docx library)
   */
  private async extractTextFromDocx(file: File): Promise<string> {
    // This is a placeholder implementation
    // In production, you would use mammoth.js or similar library
    console.warn('DOCX processing not implemented - using placeholder text');
    return `[DOCX Content from ${file.name}]\nThis is placeholder text. In production, implement DOCX text extraction using mammoth.js or similar library.`;
  }

  /**
   * Split text into chunks for processing
   */
  private splitTextIntoChunks(
    text: string,
    title: string,
    knowledgeBaseId: string
  ): TextChunk[] {
    const chunks: TextChunk[] = [];
    const maxChunkSize = PROCESSING_CONFIG.MAX_CHUNK_SIZE;
    const overlap = PROCESSING_CONFIG.CHUNK_OVERLAP;

    // Simple text splitting by sentences and paragraphs
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    let currentChunk = '';
    let chunkIndex = 0;

    for (let i = 0; i < sentences.length; i++) {
      const sentence = sentences[i].trim() + '.';
      
      if (currentChunk.length + sentence.length > maxChunkSize && currentChunk.length > 0) {
        // Create chunk
        chunks.push({
          content: currentChunk.trim(),
          chunkIndex,
          metadata: {
            source: knowledgeBaseId,
            title,
            chunkSize: currentChunk.length,
            overlap
          }
        });

        // Start new chunk with overlap
        const words = currentChunk.split(' ');
        const overlapWords = words.slice(-Math.floor(overlap / 5)); // Approximate word overlap
        currentChunk = overlapWords.join(' ') + ' ' + sentence;
        chunkIndex++;
      } else {
        currentChunk += (currentChunk ? ' ' : '') + sentence;
      }
    }

    // Add final chunk
    if (currentChunk.trim()) {
      chunks.push({
        content: currentChunk.trim(),
        chunkIndex,
        metadata: {
          source: knowledgeBaseId,
          title,
          chunkSize: currentChunk.length,
          overlap
        }
      });
    }

    return chunks;
  }

  /**
   * Store chunks in Firestore
   */
  private async storeChunks(
    chunks: TextChunk[],
    courseId: string,
    knowledgeBaseId: string
  ): Promise<number> {
    const batch = writeBatch(db);
    let batchCount = 0;
    const maxBatchSize = 500; // Firestore batch limit

    for (const chunk of chunks) {
      const chunkData: Omit<DocumentChunk, 'id'> = {
        knowledgeBaseId,
        courseId,
        content: chunk.content,
        chunkIndex: chunk.chunkIndex,
        pageNumber: chunk.pageNumber,
        section: chunk.section,
        metadata: chunk.metadata,
        createdAt: new Date()
      };

      const docRef = doc(collection(db, COLLECTIONS.DOCUMENT_CHUNKS));
      batch.set(docRef, chunkData);
      batchCount++;

      // Commit batch if it reaches the limit
      if (batchCount >= maxBatchSize) {
        await batch.commit();
        batchCount = 0;
      }
    }

    // Commit remaining items
    if (batchCount > 0) {
      await batch.commit();
    }

    return chunks.length;
  }

  /**
   * Mark knowledge base entry as processed
   */
  private async markAsProcessed(knowledgeBaseId: string, chunkCount: number): Promise<void> {
    const docRef = doc(db, COLLECTIONS.KNOWLEDGE_BASE, knowledgeBaseId);
    await updateDoc(docRef, {
      isProcessed: true,
      processedAt: new Date(),
      chunkCount
    });
  }

  /**
   * Cleanup failed processing
   */
  private async cleanupFailedProcessing(knowledgeBaseId: string): Promise<void> {
    try {
      // Delete chunks
      const chunksQuery = query(
        collection(db, COLLECTIONS.DOCUMENT_CHUNKS),
        where('knowledgeBaseId', '==', knowledgeBaseId)
      );
      const chunksSnapshot = await getDocs(chunksQuery);
      
      const batch = writeBatch(db);
      chunksSnapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });
      await batch.commit();

      // Delete knowledge base entry
      const kbRef = doc(db, COLLECTIONS.KNOWLEDGE_BASE, knowledgeBaseId);
      await updateDoc(kbRef, { isProcessed: false });

    } catch (error) {
      console.error('Cleanup error:', error);
    }
  }

  /**
   * Delete document and all associated chunks
   */
  async deleteDocument(knowledgeBaseId: string): Promise<void> {
    try {
      // Get knowledge base entry
      const kbRef = doc(db, COLLECTIONS.KNOWLEDGE_BASE, knowledgeBaseId);
      
      // Delete file from storage
      // Note: You'll need to get the filePath from the knowledge base document first
      
      // Delete all chunks
      const chunksQuery = query(
        collection(db, COLLECTIONS.DOCUMENT_CHUNKS),
        where('knowledgeBaseId', '==', knowledgeBaseId)
      );
      const chunksSnapshot = await getDocs(chunksQuery);
      
      const batch = writeBatch(db);
      chunksSnapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });
      
      // Delete knowledge base entry
      batch.delete(kbRef);
      
      await batch.commit();

    } catch (error) {
      console.error('Delete document error:', error);
      throw handleFirebaseError(error);
    }
  }
}

// Export singleton instance
export const documentProcessor = new DocumentProcessor();
