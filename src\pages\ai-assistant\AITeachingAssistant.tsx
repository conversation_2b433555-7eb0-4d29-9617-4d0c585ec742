import React, { useState, useRef, useEffect } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { 
  Bot, 
  User, 
  Send, 
  BookOpen, 
  Brain, 
  MessageSquare, 
  Loader2,
  FileText,
  GraduationCap,
  Sparkles
} from 'lucide-react';

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  sources?: string[];
}

const AITeachingAssistant = () => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: "Hello! I'm your AI Teaching Assistant for AUL courses. I can help you with questions about course materials, assignments, and academic topics. What would you like to know?",
      sender: 'ai',
      timestamp: new Date(),
      sources: []
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const courses = [
    { id: 'cs101', name: 'Introduction to Computer Science', faculty: 'Sciences' },
    { id: 'eng201', name: 'Engineering Mathematics', faculty: 'Engineering' },
    { id: 'bus301', name: 'Business Management', faculty: 'Business' },
    { id: 'arts101', name: 'Communication Arts', faculty: 'Arts & Humanities' },
  ];

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputMessage,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    // Simulate AI response (replace with actual API call)
    setTimeout(() => {
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        content: `I understand you're asking about "${inputMessage}". Based on the course materials${selectedCourse ? ` for ${courses.find(c => c.id === selectedCourse)?.name}` : ''}, here's what I can help you with. This is a simulated response - in the full implementation, this would be powered by the Gemini API and your course knowledge base.`,
        sender: 'ai',
        timestamp: new Date(),
        sources: ['Lecture Notes - Chapter 3', 'Textbook - Page 45-47']
      };

      setMessages(prev => [...prev, aiResponse]);
      setIsLoading(false);
    }, 2000);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow bg-gray-50">
        {/* Hero Section */}
        <section className="bg-gradient-to-r from-aul-navy to-aul-blue text-white py-12">
          <div className="container mx-auto px-4">
            <div className="text-center">
              <div className="flex items-center justify-center gap-3 mb-4">
                <Bot className="w-12 h-12 text-aul-gold" />
                <Sparkles className="w-8 h-8 text-aul-gold" />
              </div>
              <h1 className="text-4xl md:text-5xl font-bold mb-4">
                AUL AI Teaching Assistant
              </h1>
              <p className="text-xl md:text-2xl mb-6 max-w-3xl mx-auto">
                Get instant, accurate answers to your course questions powered by AI and AUL's comprehensive knowledge base
              </p>
              <div className="flex items-center justify-center gap-6 text-lg">
                <div className="flex items-center gap-2">
                  <Brain className="w-6 h-6 text-aul-gold" />
                  <span>AI-Powered</span>
                </div>
                <div className="flex items-center gap-2">
                  <BookOpen className="w-6 h-6 text-aul-gold" />
                  <span>Course-Specific</span>
                </div>
                <div className="flex items-center gap-2">
                  <GraduationCap className="w-6 h-6 text-aul-gold" />
                  <span>Academic Excellence</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Main Chat Interface */}
        <section className="py-8">
          <div className="container mx-auto px-4 max-w-6xl">
            <div className="grid lg:grid-cols-4 gap-6">
              {/* Course Selection Sidebar */}
              <div className="lg:col-span-1">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <BookOpen className="w-5 h-5 text-aul-gold" />
                      Select Course
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <Button
                        variant={selectedCourse === '' ? 'default' : 'outline'}
                        className="w-full justify-start"
                        onClick={() => setSelectedCourse('')}
                      >
                        General Questions
                      </Button>
                      {courses.map((course) => (
                        <Button
                          key={course.id}
                          variant={selectedCourse === course.id ? 'default' : 'outline'}
                          className="w-full justify-start text-left"
                          onClick={() => setSelectedCourse(course.id)}
                        >
                          <div>
                            <div className="font-medium">{course.name}</div>
                            <div className="text-xs text-gray-500">{course.faculty}</div>
                          </div>
                        </Button>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Features Card */}
                <Card className="mt-6">
                  <CardHeader>
                    <CardTitle className="text-lg">Features</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-start gap-2">
                        <MessageSquare className="w-4 h-4 text-aul-gold mt-1" />
                        <div className="text-sm">
                          <div className="font-medium">Instant Responses</div>
                          <div className="text-gray-600">Get answers in seconds</div>
                        </div>
                      </div>
                      <div className="flex items-start gap-2">
                        <FileText className="w-4 h-4 text-aul-gold mt-1" />
                        <div className="text-sm">
                          <div className="font-medium">Source Citations</div>
                          <div className="text-gray-600">References to course materials</div>
                        </div>
                      </div>
                      <div className="flex items-start gap-2">
                        <Brain className="w-4 h-4 text-aul-gold mt-1" />
                        <div className="text-sm">
                          <div className="font-medium">Context Aware</div>
                          <div className="text-gray-600">Understands course context</div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Chat Interface */}
              <div className="lg:col-span-3">
                <Card className="h-[600px] flex flex-col">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <MessageSquare className="w-5 h-5 text-aul-gold" />
                      Chat with AI Assistant
                      {selectedCourse && (
                        <Badge variant="secondary" className="ml-2">
                          {courses.find(c => c.id === selectedCourse)?.name}
                        </Badge>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="flex-grow flex flex-col p-0">
                    {/* Messages Area */}
                    <ScrollArea className="flex-grow p-4">
                      <div className="space-y-4">
                        {messages.map((message) => (
                          <div
                            key={message.id}
                            className={`flex gap-3 ${
                              message.sender === 'user' ? 'justify-end' : 'justify-start'
                            }`}
                          >
                            {message.sender === 'ai' && (
                              <div className="w-8 h-8 bg-aul-gold rounded-full flex items-center justify-center flex-shrink-0">
                                <Bot className="w-5 h-5 text-white" />
                              </div>
                            )}
                            <div
                              className={`max-w-[80%] rounded-lg p-3 ${
                                message.sender === 'user'
                                  ? 'bg-aul-blue text-white'
                                  : 'bg-gray-100 text-gray-900'
                              }`}
                            >
                              <div className="text-sm">{message.content}</div>
                              {message.sources && message.sources.length > 0 && (
                                <div className="mt-2 pt-2 border-t border-gray-300">
                                  <div className="text-xs text-gray-600 mb-1">Sources:</div>
                                  <div className="flex flex-wrap gap-1">
                                    {message.sources.map((source, idx) => (
                                      <Badge key={idx} variant="outline" className="text-xs">
                                        {source}
                                      </Badge>
                                    ))}
                                  </div>
                                </div>
                              )}
                              <div className="text-xs text-gray-500 mt-1">
                                {message.timestamp.toLocaleTimeString()}
                              </div>
                            </div>
                            {message.sender === 'user' && (
                              <div className="w-8 h-8 bg-aul-navy rounded-full flex items-center justify-center flex-shrink-0">
                                <User className="w-5 h-5 text-white" />
                              </div>
                            )}
                          </div>
                        ))}
                        {isLoading && (
                          <div className="flex gap-3 justify-start">
                            <div className="w-8 h-8 bg-aul-gold rounded-full flex items-center justify-center flex-shrink-0">
                              <Bot className="w-5 h-5 text-white" />
                            </div>
                            <div className="bg-gray-100 rounded-lg p-3">
                              <div className="flex items-center gap-2">
                                <Loader2 className="w-4 h-4 animate-spin" />
                                <span className="text-sm text-gray-600">AI is thinking...</span>
                              </div>
                            </div>
                          </div>
                        )}
                        <div ref={messagesEndRef} />
                      </div>
                    </ScrollArea>

                    {/* Input Area */}
                    <div className="border-t p-4">
                      <div className="flex gap-2">
                        <Input
                          value={inputMessage}
                          onChange={(e) => setInputMessage(e.target.value)}
                          onKeyPress={handleKeyPress}
                          placeholder="Ask a question about your course..."
                          className="flex-grow"
                          disabled={isLoading}
                        />
                        <Button
                          onClick={handleSendMessage}
                          disabled={!inputMessage.trim() || isLoading}
                          className="bg-aul-gold hover:bg-aul-gold/90"
                        >
                          <Send className="w-4 h-4" />
                        </Button>
                      </div>
                      <div className="text-xs text-gray-500 mt-2">
                        Press Enter to send, Shift+Enter for new line
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default AITeachingAssistant;
