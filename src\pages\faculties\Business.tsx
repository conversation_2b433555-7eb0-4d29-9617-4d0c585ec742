
import React from 'react';
import { Link } from 'react-router-dom';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { ArrowRight, BarChart, User, Mail, Phone, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselPrevious,
  CarouselNext,
} from "@/components/ui/carousel";

const businessPrograms = [
  {
    name: "Business Administration",
    description: "Comprehensive program preparing future business leaders with strong foundations in management, marketing, finance, and organizational behavior. Students develop analytical skills and strategic thinking to excel in diverse business environments.",
    slug: "business-administration",
    images: [
      {
        src: "/images/business/business 1.JPG",
        alt: "AUL Business Administration Department"
      },
      {
        src: "/images/business/DJ1A9541.JPG",
        alt: "AUL Business Faculty"
      },
      {
        src: "/images/business/IMG_3720.JPG",
        alt: "Business Class"
      },
      {
        src: "https://images.unsplash.com/photo-1600880292203-757bb62b4baf",
        alt: "Business Meeting"
      }
    ],
    highlights: [
      "Core business fundamentals and management principles",
      "Case-based learning with real-world business scenarios",
      "Internship opportunities with leading companies",
      "Focus on entrepreneurship and innovation",
      "International business perspective"
    ],
    careers: [
      "Business Manager",
      "Management Consultant",
      "Entrepreneur",
      "Project Manager",
      "Business Development Executive"
    ],
    courses: [
      "Principles of Management",
      "Organizational Behavior",
      "Financial Management",
      "Marketing Management",
      "Strategic Management",
      "Business Ethics",
      "Entrepreneurship",
      "International Business"
    ]
  },
  {
    name: "Business Management",
    description: "Specialized program focusing on developing effective management skills and leadership capabilities. Students learn to optimize organizational performance, lead teams, and implement strategic initiatives in various business contexts.",
    slug: "business-management",
    images: [
      {
        src: "/images/business/IMG_3840.JPG",
        alt: "AUL Business Management Department"
      },
      {
        src: "/images/business/FB_IMG_1734939812002.jpg",
        alt: "Business Management Class"
      },
      {
        src: "/images/business/IMG_6679.JPG",
        alt: "Management Workshop"
      },
      {
        src: "https://images.unsplash.com/photo-1462206092226-f46025ffe607",
        alt: "Business Meeting"
      }
    ],
    highlights: [
      "Advanced management theories and practices",
      "Leadership development and team management",
      "Strategic planning and implementation",
      "Change management and organizational development",
      "Business analytics for decision-making"
    ],
    careers: [
      "Operations Manager",
      "Human Resources Manager",
      "Supply Chain Manager",
      "Retail Manager",
      "Corporate Strategist"
    ],
    courses: [
      "Leadership and Change Management",
      "Human Resource Management",
      "Operations Management",
      "Project Management",
      "Supply Chain Management",
      "Business Analytics",
      "Negotiation Skills",
      "Crisis Management"
    ]
  },
  {
    name: "Finance",
    description: "Comprehensive study of financial markets, investment strategies, and corporate financial management. Students develop analytical skills to evaluate financial performance, manage investments, and make strategic financial decisions.",
    slug: "finance",
    images: [
      {
        src: "/images/business/FB_IMG_1734939842523.jpg",
        alt: "AUL Finance Department"
      },
      {
        src: "/images/business/news2.jpg",
        alt: "Finance Analysis Lab"
      },
      {
        src: "https://images.unsplash.com/photo-1611*********-9c2a0a7236a3",
        alt: "Finance Analysis"
      },
      {
        src: "https://images.unsplash.com/photo-*************-46e7ccd6d992",
        alt: "Stock Market"
      }
    ],
    highlights: [
      "Financial analysis and valuation techniques",
      "Investment management and portfolio theory",
      "Corporate finance and capital budgeting",
      "Financial markets and institutions",
      "Risk management strategies"
    ],
    careers: [
      "Financial Analyst",
      "Investment Banker",
      "Portfolio Manager",
      "Financial Consultant",
      "Risk Manager"
    ],
    courses: [
      "Financial Management",
      "Investment Analysis",
      "Corporate Finance",
      "Financial Markets and Institutions",
      "International Finance",
      "Financial Risk Management",
      "Portfolio Management",
      "Financial Derivatives"
    ]
  },
  {
    name: "Marketing",
    description: "In-depth analysis of consumer behavior and development of effective marketing strategies. Students learn to create compelling brand experiences, analyze market trends, and implement integrated marketing campaigns across various channels.",
    slug: "marketing",
    images: [
      {
        src: "/images/business/FB_IMG_1734940045243.jpg",
        alt: "AUL Marketing Department"
      },
      {
        src: "/images/business/news3.jpg",
        alt: "Marketing Workshop"
      },
      {
        src: "https://images.unsplash.com/photo-**********-d307ca884978",
        alt: "Marketing Presentation"
      },
      {
        src: "https://images.unsplash.com/photo-1516321318423-f06f85e504b3",
        alt: "Digital Marketing"
      }
    ],
    highlights: [
      "Consumer behavior analysis and market research",
      "Brand management and positioning strategies",
      "Digital marketing and social media campaigns",
      "Marketing analytics and performance measurement",
      "Integrated marketing communications"
    ],
    careers: [
      "Marketing Manager",
      "Brand Manager",
      "Digital Marketing Specialist",
      "Market Research Analyst",
      "Product Manager"
    ],
    courses: [
      "Principles of Marketing",
      "Consumer Behavior",
      "Marketing Research",
      "Brand Management",
      "Digital Marketing",
      "Marketing Analytics",
      "Integrated Marketing Communications",
      "International Marketing"
    ]
  },
  {
    name: "Accounting",
    description: "Comprehensive study of accounting principles and practices including financial reporting, taxation, and auditing. Students develop skills to analyze financial information, ensure compliance, and support strategic business decisions.",
    slug: "accounting",
    images: [
      {
        src: "/images/business/IMG_3720.JPG",
        alt: "AUL Accounting Department"
      },
      {
        src: "/images/business/DJ1A9541.JPG",
        alt: "Accounting Lab"
      },
      {
        src: "https://images.unsplash.com/photo-**********-6726b3ff858f",
        alt: "Accounting Workspace"
      },
      {
        src: "https://images.unsplash.com/photo-*************-c8848c66ca85",
        alt: "Financial Records"
      }
    ],
    highlights: [
      "Financial accounting principles and practices",
      "Managerial accounting for decision-making",
      "Taxation laws and planning strategies",
      "Auditing procedures and standards",
      "Accounting information systems"
    ],
    careers: [
      "Certified Public Accountant",
      "Financial Controller",
      "Tax Consultant",
      "Auditor",
      "Financial Reporting Specialist"
    ],
    courses: [
      "Principles of Accounting",
      "Intermediate Accounting",
      "Cost Accounting",
      "Taxation",
      "Auditing",
      "Accounting Information Systems",
      "Advanced Financial Accounting",
      "Forensic Accounting"
    ]
  },
  {
    name: "Hospitality Management",
    description: "Specialized program preparing students for careers in the hospitality industry. Students develop skills in hotel operations, event management, tourism, and customer service excellence to succeed in this dynamic global industry.",
    slug: "hospitality-management",
    images: [
      {
        src: "/images/business/IMG_6679.JPG",
        alt: "AUL Hospitality Management Department"
      },
      {
        src: "/images/business/IMG_3840.JPG",
        alt: "Hospitality Training Lab"
      },
      {
        src: "https://images.unsplash.com/photo-*************-6a8506099945",
        alt: "Hotel Lobby"
      },
      {
        src: "https://images.unsplash.com/photo-*************-2a682d7375f9",
        alt: "Event Planning"
      }
    ],
    highlights: [
      "Hotel and resort operations management",
      "Event planning and coordination",
      "Food and beverage management",
      "Tourism and destination management",
      "Customer service excellence"
    ],
    careers: [
      "Hotel Manager",
      "Event Coordinator",
      "Restaurant Manager",
      "Tourism Director",
      "Customer Experience Manager"
    ],
    courses: [
      "Introduction to Hospitality Management",
      "Hotel Operations",
      "Event Management",
      "Food and Beverage Management",
      "Tourism and Destination Marketing",
      "Customer Service Management",
      "Revenue Management",
      "Hospitality Law and Ethics"
    ]
  }
];

const facilitiesImages = [
  {
    src: "/images/business/business 1.JPG",
    alt: "AUL Business Faculty Building"
  },
  {
    src: "/images/business/DJ1A9541.JPG",
    alt: "Business Department"
  },
  {
    src: "/images/business/IMG_3720.JPG",
    alt: "Business Classroom"
  },
  {
    src: "/images/business/IMG_3840.JPG",
    alt: "Business Lab"
  },
  {
    src: "/images/business/IMG_6679.JPG",
    alt: "Business Workshop"
  },
  {
    src: "/images/business/FB_IMG_1734939812002.jpg",
    alt: "Management Class"
  },
  {
    src: "/images/business/FB_IMG_1734939842523.jpg",
    alt: "Finance Department"
  },
  {
    src: "/images/business/FB_IMG_1734940045243.jpg",
    alt: "Marketing Department"
  },
  {
    src: "/images/business/news2.jpg",
    alt: "Business News Lab"
  },
  {
    src: "/images/business/news3.jpg",
    alt: "Business Research"
  }
];

const Business = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow py-12">
        <div className="container mx-auto px-4">
          <div className="flex items-center gap-4 mb-8">
            <BarChart className="w-12 h-12 text-aul-gold" />
            <h1 className="text-4xl font-bold text-aul-navy">Faculty of Business</h1>
          </div>

          <section className="mb-12">
            <Card className="overflow-hidden">
              <Carousel className="w-full" opts={{ loop: true, duration: 20 }}>
                <CarouselContent>
                  {facilitiesImages.map((image, index) => (
                    <CarouselItem key={index}>
                      <div className="relative aspect-[21/9]">
                        <img
                          src={image.src}
                          alt={image.alt}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </CarouselItem>
                  ))}
                </CarouselContent>
                <CarouselPrevious className="left-2 z-20 bg-white text-black hover:bg-gray-200" />
                <CarouselNext className="right-2 z-20 bg-white text-black hover:bg-gray-200" />
              </Carousel>

              <CardContent className="py-6">
                <h2 className="text-2xl font-bold text-aul-navy mb-4">About the Faculty of Business</h2>
                <p className="text-gray-700 mb-4">
                  The Faculty of Business at AUL is dedicated to developing future business leaders and entrepreneurs through a comprehensive approach to business education. Our diverse programs provide students with the knowledge, skills, and experiences needed to thrive in the dynamic global business environment.
                </p>
                <p className="text-gray-700 mb-4">
                  Our curriculum combines theoretical foundations with practical applications, ensuring graduates are well-prepared for the challenges of modern business. Through case studies, internships, and industry projects, students gain hands-on experience solving real-world business problems.
                </p>
                <p className="text-gray-700">
                  With state-of-the-art facilities, experienced faculty members with extensive industry backgrounds, and strong connections to the business community, we prepare our students to make meaningful contributions to organizations across various sectors and industries. Our graduates are known for their analytical skills, ethical leadership, and innovative thinking.
                </p>
              </CardContent>
            </Card>
          </section>

          <section>
            <h2 className="text-2xl font-bold text-aul-navy mb-6">Our Programs</h2>
            <div className="grid md:grid-cols-2 gap-8">
              {businessPrograms.map((program, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow overflow-hidden">
                  <Carousel className="w-full" opts={{ loop: true, duration: 20 }}>
                    <CarouselContent>
                      {program.images.map((image, imageIndex) => (
                        <CarouselItem key={imageIndex}>
                          <div className="relative aspect-video">
                            <img
                              src={image.src}
                              alt={image.alt}
                              className="w-full h-full object-cover"
                            />
                            <div className="absolute inset-0 bg-black/20"></div>
                          </div>
                        </CarouselItem>
                      ))}
                    </CarouselContent>
                    <CarouselPrevious className="left-2 z-20 bg-white text-black hover:bg-gray-200" />
                    <CarouselNext className="right-2 z-20 bg-white text-black hover:bg-gray-200" />
                  </Carousel>

                  <CardHeader>
                    <CardTitle>{program.name}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 mb-4">{program.description}</p>

                    {program.highlights && (
                      <div className="mt-4 mb-4">
                        <h4 className="font-medium text-aul-navy mb-2">Program Highlights:</h4>
                        <ul className="list-disc pl-5 space-y-1 text-sm">
                          {program.highlights.slice(0, 3).map((highlight, idx) => (
                            <li key={idx} className="text-gray-700">{highlight}</li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {program.careers && (
                      <div className="mt-4 mb-4">
                        <h4 className="font-medium text-aul-navy mb-2">Career Opportunities:</h4>
                        <p className="text-sm text-gray-700">
                          {program.careers.slice(0, 3).join(", ")}
                          {program.careers.length > 3 ? ", and more..." : ""}
                        </p>
                      </div>
                    )}

                    {program.courses && (
                      <div className="mt-4 mb-4">
                        <h4 className="font-medium text-aul-navy mb-2">Key Courses:</h4>
                        <p className="text-sm text-gray-700">
                          {program.courses.slice(0, 3).join(", ")}
                          {program.courses.length > 3 ? ", and more..." : ""}
                        </p>
                      </div>
                    )}

                    <Button asChild variant="outline" size="sm" className="mt-4">
                      <Link to={`/programs/${program.slug}`} className="flex items-center">
                        Explore Program <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>

          <section className="mt-12">
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl font-bold text-aul-navy">Research & Innovation</CardTitle>
                <CardDescription>Advancing business knowledge through research excellence</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 mb-4">
                  The Faculty of Business at AUL is committed to advancing business knowledge through innovative research that addresses real-world challenges. Our faculty members are actively engaged in research across various business disciplines, contributing to academic literature and industry practices.
                </p>

                <h3 className="text-xl font-semibold text-aul-navy mb-3">Key Research Areas</h3>
                <div className="grid md:grid-cols-3 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-medium text-aul-navy mb-2">Finance & Economics</h4>
                    <ul className="list-disc list-inside text-gray-700 text-sm space-y-1">
                      <li>Corporate Finance</li>
                      <li>Investment Analysis</li>
                      <li>Financial Markets</li>
                      <li>Banking & FinTech</li>
                      <li>Economic Development</li>
                    </ul>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-medium text-aul-navy mb-2">Management & Leadership</h4>
                    <ul className="list-disc list-inside text-gray-700 text-sm space-y-1">
                      <li>Organizational Behavior</li>
                      <li>Strategic Management</li>
                      <li>Entrepreneurship</li>
                      <li>Business Ethics</li>
                      <li>Leadership Development</li>
                    </ul>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-medium text-aul-navy mb-2">Marketing & Consumer Studies</h4>
                    <ul className="list-disc list-inside text-gray-700 text-sm space-y-1">
                      <li>Marketing Analytics</li>
                      <li>Consumer Behavior</li>
                      <li>Digital Marketing</li>
                      <li>Brand Management</li>
                      <li>Market Research</li>
                    </ul>
                  </div>
                </div>

                <h3 className="text-xl font-semibold text-aul-navy mt-6 mb-3">Research Initiatives</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-2">
                  <li><span className="font-medium">Business Research Center:</span> Facilitates collaborative research projects between faculty, students, and industry partners.</li>
                  <li><span className="font-medium">Annual Business Conference:</span> Showcases faculty and student research, featuring presentations from international scholars and industry experts.</li>
                  <li><span className="font-medium">Business Journal:</span> Publishes peer-reviewed research articles, case studies, and industry analyses.</li>
                  <li><span className="font-medium">Student Research Program:</span> Engages undergraduate students in faculty-led research projects.</li>
                </ul>
              </CardContent>
            </Card>
          </section>

          <section className="mt-12">
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl font-bold text-aul-navy">Contact Information</CardTitle>
                <CardDescription>Get in touch with the Faculty of Business</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-8">
                  <div>
                    <h3 className="text-xl font-semibold text-aul-navy mb-4">Faculty Administration</h3>
                    <div className="space-y-3">
                      <div className="flex items-start gap-3">
                        <User className="h-5 w-5 text-aul-gold mt-0.5" />
                        <div>
                          <p className="font-medium">Faculty Dean</p>
                          <p className="text-gray-700">Dr. Marcus Chen</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <Mail className="h-5 w-5 text-aul-gold mt-0.5" />
                        <div>
                          <p className="font-medium">Email</p>
                          <p className="text-gray-700"><EMAIL></p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <Phone className="h-5 w-5 text-aul-gold mt-0.5" />
                        <div>
                          <p className="font-medium">Phone</p>
                          <p className="text-gray-700">(*************</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <MapPin className="h-5 w-5 text-aul-gold mt-0.5" />
                        <div>
                          <p className="font-medium">Location</p>
                          <p className="text-gray-700">Business Center, West Campus</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-xl font-semibold text-aul-navy mb-4">Department Contacts</h3>
                    <div className="space-y-4">
                      <div>
                        <p className="font-medium text-aul-navy">Business Administration & Management</p>
                        <p className="text-gray-700">Dr. Sarah Johnson</p>
                        <p className="text-gray-700 text-sm"><EMAIL> | (555) 345-6790</p>
                      </div>
                      <div>
                        <p className="font-medium text-aul-navy">Finance & Accounting</p>
                        <p className="text-gray-700">Dr. Robert Williams</p>
                        <p className="text-gray-700 text-sm"><EMAIL> | (555) 345-6791</p>
                      </div>
                      <div>
                        <p className="font-medium text-aul-navy">Marketing & Hospitality</p>
                        <p className="text-gray-700">Dr. Emily Rodriguez</p>
                        <p className="text-gray-700 text-sm"><EMAIL> | (555) 345-6792</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-8 pt-6 border-t border-gray-200">
                  <h3 className="text-xl font-semibold text-aul-navy mb-4">Office Hours</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <p className="font-medium">Monday - Thursday</p>
                      <p className="text-gray-700">8:30 AM - 4:30 PM</p>
                    </div>
                    <div>
                      <p className="font-medium">Friday</p>
                      <p className="text-gray-700">8:30 AM - 3:00 PM</p>
                    </div>
                    <div>
                      <p className="font-medium">Saturday</p>
                      <p className="text-gray-700">9:00 AM - 1:00 PM</p>
                    </div>
                    <div>
                      <p className="font-medium">Sunday</p>
                      <p className="text-gray-700">Closed</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </section>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Business;
