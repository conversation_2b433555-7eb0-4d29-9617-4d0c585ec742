
@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: 'Open Sans', sans-serif;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Montserrat', sans-serif;
}

.aul-container {
  width: 100%;
  max-width: 1280px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* University colors */
:root {
  --aul-navy: #1B365D;
  --aul-gold: #C8A882;
  --aul-red: #8B0000;
  --aul-blue: #2E5984;
  --aul-lightblue: #4A90B8;
  --aul-gray: #F5F5F5;
  --aul-darkgray: #2C3E50;
  --aul-white: #FFFFFF;
}

.bg-aul-navy {
  background-color: var(--aul-navy);
}

.bg-aul-gold {
  background-color: var(--aul-gold);
}

.bg-aul-red {
  background-color: var(--aul-red);
}

.bg-aul-blue {
  background-color: var(--aul-blue);
}

.bg-aul-lightblue {
  background-color: var(--aul-lightblue);
}

.bg-aul-gray {
  background-color: var(--aul-gray);
}

.bg-aul-darkgray {
  background-color: var(--aul-darkgray);
}

.text-aul-navy {
  color: var(--aul-navy);
}

.text-aul-gold {
  color: var(--aul-gold);
}

.text-aul-red {
  color: var(--aul-red);
}

.text-aul-blue {
  color: var(--aul-blue);
}

.text-aul-lightblue {
  color: var(--aul-lightblue);
}

.text-aul-darkgray {
  color: var(--aul-darkgray);
}

.border-aul-navy {
  border-color: var(--aul-navy);
}

.border-aul-gold {
  border-color: var(--aul-gold);
}

.border-aul-red {
  border-color: var(--aul-red);
}

.border-aul-blue {
  border-color: var(--aul-blue);
}

.border-aul-lightblue {
  border-color: var(--aul-lightblue);
}

/* Animations */
.animate-fade-in {
  animation: fadeIn 0.6s ease-in;
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
