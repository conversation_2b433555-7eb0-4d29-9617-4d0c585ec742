# AUL AI Teaching Assistant

An AI-powered teaching assistant for the American University of Lebanon (AUL) that provides students with instant, accurate answers to course-related questions using advanced AI technology and Retrieval-Augmented Generation (RAG).

## 🎯 Features

- **AI-Powered Responses**: Powered by Google's Gemini API for intelligent, contextual answers
- **Course-Specific Knowledge**: Access to comprehensive course materials and textbooks
- **Real-time Chat Interface**: Instant responses with typing indicators and smooth UX
- **Source Citations**: Every answer includes references to specific course materials
- **Multi-Course Support**: Switch between different courses and contexts
- **User Authentication**: Secure login and personalized experience
- **Conversation History**: Save and resume previous conversations
- **Admin Panel**: Upload and manage course materials
- **Responsive Design**: Works perfectly on desktop and mobile devices

## 🏗️ Architecture

### Frontend (React + TypeScript)
- **Framework**: React 18 with TypeScript
- **Routing**: React Router v6
- **Styling**: Tailwind CSS with Radix UI components
- **State Management**: React Context API
- **Build Tool**: Vite

### Backend (Node.js + Express)
- **Runtime**: Node.js with Express.js
- **Database**: Google Firestore (NoSQL)
- **Storage**: Firebase Storage for documents
- **Authentication**: Firebase Auth
- **AI Integration**: Google Gemini API

### AI & RAG Pipeline
- **LLM**: Google Gemini Pro
- **Vector Search**: Firestore-based similarity search
- **Document Processing**: PDF, DOCX, TXT, Markdown support
- **Knowledge Base**: Chunked course materials with metadata

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm 9+
- Firebase project with Firestore and Storage enabled
- Google Gemini API key

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/aul-edu/ai-teaching-assistant.git
   cd aul-ai-assistant
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` with your configuration:
   ```env
   # Firebase Configuration
   VITE_FIREBASE_API_KEY=your_firebase_api_key
   VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
   VITE_FIREBASE_PROJECT_ID=your_project_id
   VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
   VITE_FIREBASE_MESSAGING_SENDER_ID=123456789
   VITE_FIREBASE_APP_ID=your_app_id

   # Gemini API
   VITE_GEMINI_API_KEY=your_gemini_api_key

   # Server Configuration
   PORT=5000
   NODE_ENV=development
   ```

4. **Start the development servers**
   
   Frontend:
   ```bash
   npm run dev
   ```
   
   Backend (in a new terminal):
   ```bash
   npm run server:dev
   ```

5. **Open your browser**
   Navigate to `http://localhost:3000`

## 📁 Project Structure

```
aul-ai-assistant/
├── src/                          # Frontend source code
│   ├── components/               # Reusable React components
│   │   ├── auth/                # Authentication components
│   │   ├── chat/                # Chat interface components
│   │   ├── layout/              # Layout components
│   │   └── ui/                  # UI components (buttons, cards, etc.)
│   ├── contexts/                # React Context providers
│   ├── hooks/                   # Custom React hooks
│   ├── pages/                   # Page components
│   ├── services/                # API services and utilities
│   ├── types/                   # TypeScript type definitions
│   └── utils/                   # Utility functions
├── server/                      # Backend source code
│   ├── controllers/             # Route controllers
│   ├── middleware/              # Express middleware
│   ├── routes/                  # API routes
│   ├── services/                # Business logic services
│   └── utils/                   # Server utilities
├── public/                      # Static assets
└── docs/                        # Documentation
```

## 🔧 Configuration

### Firebase Setup

1. Create a new Firebase project at [Firebase Console](https://console.firebase.google.com)
2. Enable Authentication, Firestore, and Storage
3. Create a web app and copy the configuration
4. Set up Firestore security rules (see `firestore.rules`)
5. Configure Storage rules (see `storage.rules`)

### Gemini API Setup

1. Get your API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Add it to your `.env` file
3. Ensure you have sufficient quota for your usage

## 📚 Usage

### For Students

1. **Sign Up/Login**: Create an account or sign in with your AUL credentials
2. **Select Course**: Choose the course you need help with
3. **Ask Questions**: Type your questions in natural language
4. **Get Answers**: Receive instant, accurate responses with source citations
5. **Review Sources**: Click on citations to see the original course materials

### For Administrators

1. **Access Admin Panel**: Navigate to `/admin/knowledge-base`
2. **Upload Documents**: Add course materials (PDFs, DOCX, etc.)
3. **Manage Courses**: Create and organize course structures
4. **Monitor Usage**: View analytics and user interactions

## 🛠️ Development

### Available Scripts

```bash
# Frontend development
npm run dev              # Start development server
npm run build           # Build for production
npm run preview         # Preview production build
npm run lint            # Run ESLint
npm run type-check      # Run TypeScript checks

# Backend development
npm run server:dev      # Start backend in development mode
npm run server:build    # Build backend for production
npm run server:start    # Start production backend

# Full stack
npm run setup           # Install all dependencies
```

### Adding New Features

1. **Frontend Components**: Add to `src/components/`
2. **API Endpoints**: Add to `server/routes/`
3. **Database Models**: Update `src/types/` and Firestore rules
4. **AI Prompts**: Modify `server/services/aiService.js`

## 🚀 Deployment

### Frontend Deployment (Vercel/Netlify)

1. Build the project: `npm run build`
2. Deploy the `dist/` folder to your hosting service
3. Set environment variables in your hosting dashboard

### Backend Deployment (Google Cloud/AWS/Railway)

1. Build the server: `npm run server:build`
2. Deploy the `dist/server/` folder
3. Set environment variables
4. Ensure Firebase credentials are properly configured

### Environment Variables for Production

```env
NODE_ENV=production
PORT=5000
FIREBASE_PROJECT_ID=your_project_id
GEMINI_API_KEY=your_gemini_api_key
CORS_ORIGIN=https://your-frontend-domain.com
```

## 📊 Monitoring & Analytics

- **Firebase Analytics**: Track user engagement and feature usage
- **Error Monitoring**: Implement Sentry or similar for error tracking
- **Performance**: Monitor API response times and AI processing speed
- **Usage Metrics**: Track questions asked, courses accessed, user satisfaction

## 🔒 Security

- **Authentication**: Firebase Auth with email/password and social logins
- **Authorization**: Role-based access control (student, instructor, admin)
- **Data Privacy**: All conversations are encrypted and user-specific
- **API Security**: Rate limiting, input validation, and secure headers
- **Firestore Rules**: Strict database security rules

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the `/docs` folder for detailed guides
- **Issues**: Report bugs and request features on GitHub Issues
- **Contact**: reach out to the AUL IT department for technical support

## 🙏 Acknowledgments

- **Google Gemini**: For providing the AI capabilities
- **Firebase**: For the backend infrastructure
- **React Community**: For the amazing ecosystem
- **AUL Faculty**: For providing course materials and feedback

---

**Built with ❤️ for AUL students by the AUL Development Team**
