import React, { useEffect, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselPrevious,
  CarouselNext,
} from "@/components/ui/carousel"
import { Slider } from "@/components/ui/slider";

const backgroundImages = [
  {
    src: "/images/home/<USER>",
    alt: "AUL Jadra Campus"
  },
  {
    src: "/images/home/<USER>",
    alt: "AUL Campus Building"
  },
  {
    src: "/images/home/<USER>",
    alt: "AUL Dekwaneh Campus"
  },
  {
    src: "/images/home/<USER>",
    alt: "AUL College of Arts"
  },
  {
    src: "/images/home/<USER>",
    alt: "AUL Engineering Laboratory"
  },
  {
    src: "/images/home/<USER>",
    alt: "AUL Campus Facilities"
  }
];

const Hero = () => {
  const [api, setApi] = useState<any>(null);
  const [current, setCurrent] = useState(0);

  useEffect(() => {
    if (!api) {
      return;
    }

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap());
    });
  }, [api]);

  const handleSliderChange = (value: number[]) => {
    if (api) {
      api.scrollTo(value[0]);
    }
  };

  return (
    <section className="relative h-[90vh] min-h-[600px] flex items-center overflow-hidden">
      {/* Hero background image carousel */}
      <div className="absolute inset-0 z-0">
        {/* Semi-transparent overlay for better text readability */}
        <div className="absolute inset-0 bg-black/20 z-10"></div>
        <Carousel
          className="w-full h-full relative"
          opts={{
            loop: true,
            align: "center",
          }}
          setApi={setApi}
        >
          <CarouselContent className="h-full">
            {backgroundImages.map((image, index) => (
              <CarouselItem key={index} className="h-full pl-0">
                <img
                  src={image.src}
                  alt={image.alt}
                  className="w-full h-full object-contain bg-black"
                />
              </CarouselItem>
            ))}
          </CarouselContent>
          <div className="absolute inset-0 flex items-center justify-between p-4 pointer-events-none">
            <CarouselPrevious className="relative pointer-events-auto left-4 z-20 bg-white text-black hover:bg-gray-200" />
            <CarouselNext className="relative pointer-events-auto right-4 z-20 bg-white text-black hover:bg-gray-200" />
          </div>
        </Carousel>
      </div>

      {/* Hero content */}
      <div className="aul-container relative z-10 text-white">
        <div className="max-w-3xl flex flex-col items-center">
          {/* Logo */}
          <img
            src="/lovable-uploads/84495c26-350f-4fc4-ac8e-0766d47be600.png"
            alt="AUL University Logo"
            className="h-20 w-20 rounded-full mb-4 border-2 border-white bg-white object-cover shadow"
            style={{ background: "white" }}
          />
          <h1 className="text-5xl md:text-6xl font-bold mb-4 animate-fade-in text-center">
            Welcome to AUL
          </h1>
          <p className="text-xl md:text-2xl mb-8 animate-fade-in text-center" style={{animationDelay: '0.2s'}}>
            Shaping Tomorrow's Leaders Through Excellence in Education
          </p>
          <div className="flex flex-wrap gap-4 animate-fade-in" style={{animationDelay: '0.4s'}}>
            <Button className="bg-aul-gold hover:bg-aul-gold/90 text-aul-navy text-lg py-6 px-8 font-semibold shadow-lg" asChild>
              <Link to="/admissions/apply">Apply Now</Link>
            </Button>
            <Button variant="outline" className="border-2 border-white text-white hover:bg-white hover:text-aul-navy text-lg py-6 px-8 font-semibold" asChild>
              <Link to="/virtual-tour">Virtual Tour</Link>
            </Button>
            <Button variant="outline" className="border-2 border-aul-gold text-aul-gold hover:bg-aul-gold hover:text-aul-navy text-lg py-6 px-8 font-semibold" asChild>
              <Link to="/contact/request-info">Request Info</Link>
            </Button>
          </div>
        </div>
      </div>

      {/* Image slider dots indicator */}
      <div className="absolute bottom-8 left-0 right-0 z-20 flex justify-center gap-2">
        <div className="w-48">
          <Slider
            value={[current]}
            max={backgroundImages.length - 1}
            step={1}
            onValueChange={handleSliderChange}
            className="bg-white/30 h-1 rounded-full"
          />
        </div>
      </div>

      {/* Scrolling indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <svg
          className="w-8 h-8 text-white"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 14l-7 7m0 0l-7-7m7 7V3"
          />
        </svg>
      </div>
    </section>
  );
};

export default Hero;
