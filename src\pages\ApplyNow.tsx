
import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/hooks/use-toast";
import { Check } from "lucide-react";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";

// Use your actual Formspree endpoint here!
const FORMSPREE_ENDPOINT = "https://formspree.io/f/mwkgldjk";

const programs = [
  "BSc in Computer Science",
  "BSc in Biology",
  "BSc in Mathematics",
  "BSc in Chemistry",
  "MSc in Artificial Intelligence",
  "MSc in Molecular Biology",
  "MSc in Applied Mathematics",
  "MSc in Chemical Sciences",
  "PhD in Computer Science",
  "PhD in Life Sciences",
  "PhD in Mathematics",
  "PhD in Chemistry",
];

const ApplyNow = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const form = e.currentTarget;
    setIsSubmitting(true);

    const formData = new FormData(form);

    try {
      const response = await fetch(FORMSPREE_ENDPOINT, {
        method: "POST",
        body: formData,
        headers: {
          Accept: "application/json",
        },
      });

      if (response.ok) {
        toast({
          title: "Application submitted!",
          description:
            "Thank you for your application. Our admissions team will contact you soon.",
        });
        form.reset();
      } else {
        toast({
          title: "Submission failed",
          description: "Something went wrong. Please try again later.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Submission failed",
        description: "Network error. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-purple-50 via-white to-purple-100">
      <Header />
      <main className="flex-1 flex items-center justify-center py-12">
        <div className="max-w-xl w-full mx-4 bg-white rounded-xl shadow-md p-8 border border-purple-100 animate-fade-in">
          <div className="flex flex-col items-center mb-8">
            <img
              src="/lovable-uploads/84495c26-350f-4fc4-ac8e-0766d47be600.png"
              alt="AUL University Logo"
              className="h-32 w-32 mb-4 rounded-full object-cover border-4 border-white shadow"
              style={{ background: "white" }}
            />
            <h1 className="text-3xl font-bold text-center mb-2 text-purple-900">
              Apply Now — University Application
            </h1>
          </div>
          <p className="text-center text-gray-600 mb-8">
            Start your journey at our university! Complete the form below to submit your application for any of our outstanding academic programs.
          </p>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-purple-900 mb-1">
                Full Name
              </label>
              <Input required id="name" name="name" placeholder="Your Name" />
            </div>
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-purple-900 mb-1">
                Email
              </label>
              <Input
                required
                id="email"
                name="email"
                type="email"
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <label htmlFor="program" className="block text-sm font-medium text-purple-900 mb-1">
                Program of Interest
              </label>
              <select
                required
                id="program"
                name="program"
                className="w-full border border-input rounded-md px-3 py-2 bg-background text-base focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-purple-400"
                defaultValue=""
              >
                <option value="" disabled>
                  Select a program…
                </option>
                {programs.map((p) => (
                  <option key={p} value={p}>
                    {p}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label htmlFor="statement" className="block text-sm font-medium text-purple-900 mb-1">
                Motivation Statement
              </label>
              <Textarea
                required
                id="statement"
                name="statement"
                placeholder="Tell us why you want to join our university…"
                minLength={20}
                maxLength={1000}
              />
              <span className="text-xs text-gray-400">20–1000 characters</span>
            </div>
            <Button
              type="submit"
              size="lg"
              className="w-full bg-purple-700 hover:bg-purple-800"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Submitting..." : "Submit Application"}
              <Check className="ml-2" />
            </Button>
          </form>
          <p className="text-xs text-center text-gray-400 mt-6">
            Your application will be sent securely to the admissions office and you'll receive a confirmation email.
          </p>
        </div>
      </main>
      <Footer />
    </div>
  );
};
export default ApplyNow;
