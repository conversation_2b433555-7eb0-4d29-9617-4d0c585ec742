import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Calendar, Clock, ArrowRight, MapPin } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { Badge } from "@/components/ui/badge";

const newsItems = [
  {
    id: 1,
    title: "AUL Signs Cooperation Agreement with Badr Foundation",
    excerpt: "The University of Arts, Sciences, and Technology in Lebanon (AUL) signed a cooperation agreement with the Badr Foundation, represented by its president, MP <PERSON><PERSON>, to support students facing financial difficulties that prevent them from pursuing their university education.",
    date: "January 20, 2025",
    image: "/images/news1.jpg",
    category: "Partnership",
    url: "/news-events/news/badr-foundation-agreement"
  },
  {
    id: 2,
    title: "English Department Celebrates English Day at AUL Beirut",
    excerpt: "In the presence of President Prof<PERSON> and faculty members, the English Department celebrated English Day at AUL Beirut. The event emphasized the importance of English language mastery for academic and professional advancement.",
    date: "January 15, 2025",
    image: "/images/news2.jpg",
    category: "Academic Events",
    url: "/news-events/news/english-day-beirut"
  },
  {
    id: 3,
    title: "AUL Chtaura Campus Celebrates English Day with Student Presentations",
    excerpt: "AUL Chtaura celebrated English Day, showcasing the richness and global significance of the English language. Students presented speeches on English as a global language, its importance in technology and business, and featured discussions on Shakespeare's works.",
    date: "January 12, 2025",
    image: "/images/news3.jpg",
    category: "Campus Events",
    url: "/news-events/news/english-day-chtaura"
  }
];

const eventsItems = [
  {
    id: 1,
    title: "Spring 2025 Orientation Week",
    description: "Welcome week for new Spring semester students featuring campus tours, academic advising, and social activities to help students integrate into AUL community.",
    date: "February 3-7, 2025",
    time: "9:00 AM - 4:00 PM",
    location: "All AUL Campuses",
    url: "/news-events/events/spring-orientation-2025"
  },
  {
    id: 2,
    title: "Career Fair 2025 - Connect with Top Employers",
    description: "Annual career fair bringing together leading companies from Lebanon and the region to meet AUL students and graduates for internships and job opportunities.",
    date: "February 15, 2025",
    time: "10:00 AM - 4:00 PM",
    location: "AUL Main Campus - Sports Hall",
    url: "/news-events/events/career-fair-2025"
  },
  {
    id: 3,
    title: "International Women's Day Symposium",
    description: "Special symposium celebrating women in STEM fields, featuring successful AUL alumnae and industry leaders sharing their experiences and insights.",
    date: "March 8, 2025",
    time: "2:00 PM - 6:00 PM",
    location: "AUL Conference Center, Kaslik",
    url: "/news-events/events/womens-day-symposium-2025"
  },
  {
    id: 4,
    title: "AUL Innovation Showcase",
    description: "Student-led exhibition showcasing innovative projects, research, and startup ideas from across all faculties. Open to public and industry partners.",
    date: "March 20, 2025",
    time: "1:00 PM - 7:00 PM",
    location: "AUL Innovation Hub, Dekwaneh Campus",
    url: "/news-events/events/innovation-showcase-2025"
  }
];

const NewsEvents = () => {
  return (
    <section className="section-padding bg-aul-gray">
      <div className="aul-container">
        <div className="flex flex-col md:flex-row justify-between items-center mb-8">
          <div className="text-center md:text-left mb-4 md:mb-0">
            <h2 className="text-3xl font-bold text-aul-navy mb-2">News & Events</h2>
            <div className="h-1 w-16 bg-aul-gold mx-auto md:mx-0 rounded-full"></div>
          </div>
          <div className="w-full md:w-auto">
            <Tabs defaultValue="news" className="w-full">
              <TabsList className="w-full md:w-auto grid grid-cols-2 mb-6">
                <TabsTrigger value="news" className="text-base">Latest News</TabsTrigger>
                <TabsTrigger value="events" className="text-base">Upcoming Events</TabsTrigger>
              </TabsList>

              <TabsContent value="news" className="mt-0">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {newsItems.map((item) => (
                    <Card key={item.id} className="overflow-hidden card-hover h-full flex flex-col bg-white transform transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
                      <div className="aspect-video overflow-hidden">
                        <img
                          src={item.image}
                          alt={item.title}
                          className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
                        />
                      </div>
                      <CardHeader>
                        <div className="flex items-center gap-2 mb-2">
                          <Badge variant="secondary" className="bg-aul-blue/10 text-aul-navy hover:bg-aul-blue/20 border border-aul-blue/20">
                            {item.category}
                          </Badge>
                          <span className="text-sm text-gray-500">
                            {item.date}
                          </span>
                        </div>
                        <CardTitle className="text-xl line-clamp-2 hover:text-aul-blue transition-colors">
                          {item.title}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="flex-grow">
                        <p className="text-gray-600 line-clamp-3">{item.excerpt}</p>
                      </CardContent>
                      <CardFooter>
                        <Link
                          to={item.url}
                          className="text-aul-blue font-medium flex items-center hover:text-aul-navy transition-colors"
                        >
                          Read More <ArrowRight size={16} className="ml-1" />
                        </Link>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
                <div className="mt-8 text-center">
                  <Button variant="outline" asChild className="min-w-[150px]">
                    <Link to="/news-events/news">View All News</Link>
                  </Button>
                </div>
              </TabsContent>

              <TabsContent value="events" className="mt-0">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {eventsItems.map((event) => (
                    <Card key={event.id} className="card-hover flex flex-col h-full bg-white transform transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
                      <CardHeader>
                        <CardTitle className="text-xl hover:text-aul-blue transition-colors">
                          {event.title}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="flex-grow">
                        <CardDescription className="text-base mb-4">
                          {event.description}
                        </CardDescription>
                        <div className="space-y-3">
                          <div className="flex items-start text-gray-600">
                            <Calendar className="h-5 w-5 text-aul-blue mr-2 mt-0.5 flex-shrink-0" />
                            <span>{event.date}</span>
                          </div>
                          <div className="flex items-start text-gray-600">
                            <Clock className="h-5 w-5 text-aul-blue mr-2 mt-0.5 flex-shrink-0" />
                            <span>{event.time}</span>
                          </div>
                          <div className="flex items-start text-gray-600">
                            <MapPin className="h-5 w-5 text-aul-blue mr-2 mt-0.5 flex-shrink-0" />
                            <span>{event.location}</span>
                          </div>
                        </div>
                      </CardContent>
                      <CardFooter>
                        <Link
                          to={event.url}
                          className="text-aul-blue font-medium flex items-center hover:text-aul-navy transition-colors"
                        >
                          Event Details <ArrowRight size={16} className="ml-1" />
                        </Link>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
                <div className="mt-8 text-center">
                  <Button variant="outline" asChild className="min-w-[150px]">
                    <Link to="/news-events/events">View All Events</Link>
                  </Button>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </section>
  );
};

export default NewsEvents;
