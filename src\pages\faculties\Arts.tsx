
import React from 'react';
import { Link } from 'react-router-dom';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { ArrowRight, Palette, User, Mail, Phone, MapPin } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselPrevious,
  CarouselNext,
} from "@/components/ui/carousel";

const artsPrograms = [
  {
    name: "Communication Arts",
    description: "A comprehensive program focusing on various aspects of communication including journalism, film, radio, and television. Students develop skills in media production, storytelling, and critical analysis of media content.",
    slug: "communication-arts",
    images: [
      {
        src: "/images/arts/arts 1.JPG",
        alt: "AUL Communication Arts Department"
      },
      {
        src: "/images/arts/DJ1A9584.JPG",
        alt: "AUL Arts Faculty"
      },
      {
        src: "https://images.unsplash.com/photo-1581291518633-83b4ebd1d83e",
        alt: "Media Production"
      },
      {
        src: "https://images.unsplash.com/photo-1542744095-fcf48d80b0fd",
        alt: "Digital Content Creation"
      }
    ],
    highlights: [
      "Hands-on experience with professional media equipment",
      "Industry-standard production studios",
      "Opportunities to create real media projects",
      "Focus on both theoretical and practical aspects",
      "Preparation for diverse media careers"
    ],
    careers: [
      "Journalist",
      "Film/TV Producer",
      "Radio Broadcaster",
      "Media Analyst",
      "Content Creator",
      "Public Relations Specialist",
      "Social Media Manager",
      "Documentary Filmmaker"
    ],
    courses: [
      "Introduction to Mass Communication",
      "Media Writing and Reporting",
      "Film and TV Production",
      "Radio Broadcasting",
      "Digital Journalism",
      "Media Ethics and Law",
      "Documentary Production",
      "Media Research Methods"
    ]
  },
  {
    name: "Film & Radio TV",
    description: "Specialized program focusing on the art and craft of film, television, and radio production. Students learn scriptwriting, directing, cinematography, editing, and sound design while developing their creative vision.",
    slug: "film-radio-tv",
    images: [
      {
        src: "/images/arts/arts2.JPG",
        alt: "AUL Film & Radio TV Department"
      },
      {
        src: "/images/arts/arts 1.JPG",
        alt: "AUL Media Studio"
      },
      {
        src: "https://images.unsplash.com/photo-1540204597-3c31c307c1d3",
        alt: "Film Production"
      },
      {
        src: "https://images.unsplash.com/photo-1485846234645-a62644f84728",
        alt: "TV Studio"
      }
    ],
    highlights: [
      "Professional-grade film and audio equipment",
      "Dedicated studios for film, TV, and radio production",
      "Industry professionals as guest lecturers",
      "Student film festivals and showcases",
      "Collaborative production projects"
    ],
    careers: [
      "Film Director",
      "Cinematographer",
      "TV Producer",
      "Radio Host",
      "Video Editor",
      "Sound Designer",
      "Screenwriter",
      "Production Manager"
    ],
    courses: [
      "Film Direction",
      "Cinematography",
      "Screenwriting",
      "Sound Design",
      "Video Editing",
      "TV Studio Production",
      "Radio Programming",
      "Film Theory and Criticism"
    ]
  },
  {
    name: "Journalism",
    description: "Program dedicated to developing skilled journalists who can report accurately, ethically, and effectively across various media platforms. Students learn investigative techniques, writing, and multimedia storytelling.",
    slug: "journalism",
    images: [
      {
        src: "/images/arts/DJ1A9584.JPG",
        alt: "AUL Journalism Department"
      },
      {
        src: "/images/arts/arts2.JPG",
        alt: "Journalism Lab"
      },
      {
        src: "https://images.unsplash.com/photo-1504711434969-e33886168f5c",
        alt: "Journalism Work"
      },
      {
        src: "https://images.unsplash.com/photo-1495020689067-958852a7765e",
        alt: "News Reporting"
      }
    ],
    highlights: [
      "Hands-on reporting experience",
      "Student-run news publications",
      "Focus on digital and multimedia journalism",
      "Investigative journalism projects",
      "Media ethics and legal training"
    ],
    careers: [
      "News Reporter",
      "Editor",
      "Investigative Journalist",
      "Photojournalist",
      "Digital Content Producer",
      "Foreign Correspondent",
      "Feature Writer",
      "Broadcast Journalist"
    ],
    courses: [
      "News Reporting and Writing",
      "Investigative Journalism",
      "Digital Journalism",
      "Media Ethics",
      "Photojournalism",
      "Feature Writing",
      "Broadcast News",
      "International Journalism"
    ]
  },
  {
    name: "English Language & Literature",
    description: "Comprehensive study of English language, literature, and culture. Students develop critical thinking, analytical skills, and effective communication while exploring diverse literary traditions and linguistic principles.",
    slug: "english-literature",
    images: [
      {
        src: "/images/arts/language1.JPG",
        alt: "AUL English Language & Literature Department"
      },
      {
        src: "/images/arts/arts 1.JPG",
        alt: "Literature Classroom"
      },
      {
        src: "https://images.unsplash.com/photo-1524578271613-d550eacf6090",
        alt: "Literature Collection"
      },
      {
        src: "https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0",
        alt: "Reading Room"
      }
    ],
    highlights: [
      "Comprehensive coverage of literary periods and genres",
      "Focus on critical analysis and interpretation",
      "Development of advanced writing skills",
      "Linguistic theory and language studies",
      "Creative writing opportunities"
    ],
    careers: [
      "Teacher/Professor",
      "Editor",
      "Content Writer",
      "Translator",
      "Literary Critic",
      "Publishing Professional",
      "Communications Specialist",
      "Researcher"
    ],
    courses: [
      "Introduction to Literary Studies",
      "British Literature",
      "American Literature",
      "World Literature",
      "Literary Theory and Criticism",
      "Linguistics",
      "Creative Writing",
      "Shakespeare Studies"
    ]
  },
  {
    name: "Teaching Diploma",
    description: "Professional program preparing students for careers in education. The curriculum combines educational theory, teaching methodologies, and practical classroom experience to develop effective educators.",
    slug: "teaching-diploma",
    images: [
      {
        src: "/images/arts/education 1.JPG",
        alt: "AUL Teaching Diploma Program"
      },
      {
        src: "/images/arts/language1.JPG",
        alt: "Education Department"
      },
      {
        src: "https://images.unsplash.com/photo-1577896851231-70ef18881754",
        alt: "Classroom Teaching"
      },
      {
        src: "https://images.unsplash.com/photo-1503676260728-1c00da094a0b",
        alt: "Education Studies"
      }
    ],
    highlights: [
      "Comprehensive teaching methodologies",
      "Classroom management techniques",
      "Educational psychology and child development",
      "Practical teaching experience",
      "Curriculum design and assessment"
    ],
    careers: [
      "School Teacher",
      "Educational Administrator",
      "Curriculum Developer",
      "Educational Consultant",
      "Special Education Teacher",
      "Corporate Trainer",
      "Educational Technology Specialist",
      "Academic Advisor"
    ],
    courses: [
      "Educational Psychology",
      "Teaching Methods",
      "Classroom Management",
      "Curriculum Development",
      "Educational Technology",
      "Assessment and Evaluation",
      "Special Education",
      "Teaching Practicum"
    ]
  }
];

const facilitiesImages = [
  {
    src: "/images/arts/arts 1.JPG",
    alt: "AUL Arts Faculty Building"
  },
  {
    src: "/images/arts/arts2.JPG",
    alt: "Arts Department"
  },
  {
    src: "/images/arts/DJ1A9584.JPG",
    alt: "Arts & Humanities Center"
  },
  {
    src: "/images/arts/education 1.JPG",
    alt: "Education Department"
  },
  {
    src: "/images/arts/language1.JPG",
    alt: "Language & Literature Department"
  },
  {
    src: "https://images.unsplash.com/photo-1526314149856-56505f3cbe6e",
    alt: "Arts Building"
  },
  {
    src: "https://images.unsplash.com/photo-1513364776144-60967b0f800f",
    alt: "Art Studio"
  },
  {
    src: "https://images.unsplash.com/photo-1541018939203-36eeab6d5721",
    alt: "Media Lab"
  }
];

const Arts = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow py-12">
        <div className="container mx-auto px-4">
          <div className="flex items-center gap-4 mb-8">
            <Palette className="w-12 h-12 text-aul-gold" />
            <h1 className="text-4xl font-bold text-aul-navy">Faculty of Arts & Humanities</h1>
          </div>

          <section className="mb-12">
            <Card className="overflow-hidden">
              <Carousel className="w-full" opts={{ loop: true, duration: 20 }}>
                <CarouselContent>
                  {facilitiesImages.map((image, index) => (
                    <CarouselItem key={index}>
                      <div className="relative aspect-[21/9]">
                        <img
                          src={image.src}
                          alt={image.alt}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </CarouselItem>
                  ))}
                </CarouselContent>
                <CarouselPrevious className="left-2 z-20 bg-white text-black hover:bg-gray-200" />
                <CarouselNext className="right-2 z-20 bg-white text-black hover:bg-gray-200" />
              </Carousel>

              <CardContent className="py-6">
                <h2 className="text-2xl font-bold text-aul-navy mb-4">About the Faculty of Arts & Humanities</h2>
                <p className="text-gray-700 mb-4">
                  The Faculty of Arts and Humanities at AUL fosters creativity, critical thinking, and cultural understanding. Our diverse programs in Communication Arts, Film & Radio TV, Journalism, English Language & Literature, and Education prepare students to become thoughtful creators, communicators, and educators in an increasingly complex global environment.
                </p>
                <p className="text-gray-700 mb-4">
                  Our curriculum combines theoretical foundations with practical applications, ensuring graduates are well-prepared for the challenges of their chosen fields. Through hands-on projects, internships, and collaborative work, students gain valuable experience solving real-world problems and developing professional skills.
                </p>
                <p className="text-gray-700">
                  With dedicated studios, cutting-edge technology, and passionate faculty members with extensive professional experience, we provide our students with the environment and guidance they need to develop their unique voices and make meaningful contributions to society. Our graduates are known for their creativity, critical thinking abilities, and effective communication skills.
                </p>
              </CardContent>
            </Card>
          </section>

          <section>
            <h2 className="text-2xl font-bold text-aul-navy mb-6">Our Programs</h2>
            <div className="grid md:grid-cols-2 gap-8">
              {artsPrograms.map((program, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow overflow-hidden">
                  <Carousel className="w-full" opts={{ loop: true, duration: 20 }}>
                    <CarouselContent>
                      {program.images.map((image, imageIndex) => (
                        <CarouselItem key={imageIndex}>
                          <div className="relative aspect-video">
                            <img
                              src={image.src}
                              alt={image.alt}
                              className="w-full h-full object-cover"
                            />
                            <div className="absolute inset-0 bg-black/20"></div>
                          </div>
                        </CarouselItem>
                      ))}
                    </CarouselContent>
                    <CarouselPrevious className="left-2 z-20 bg-white text-black hover:bg-gray-200" />
                    <CarouselNext className="right-2 z-20 bg-white text-black hover:bg-gray-200" />
                  </Carousel>

                  <CardHeader>
                    <CardTitle>{program.name}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 mb-4">{program.description}</p>

                    {program.highlights && (
                      <div className="mt-4 mb-4">
                        <h4 className="font-medium text-aul-navy mb-2">Program Highlights:</h4>
                        <ul className="list-disc pl-5 space-y-1 text-sm">
                          {program.highlights.slice(0, 3).map((highlight, idx) => (
                            <li key={idx} className="text-gray-700">{highlight}</li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {program.careers && (
                      <div className="mt-4 mb-4">
                        <h4 className="font-medium text-aul-navy mb-2">Career Opportunities:</h4>
                        <p className="text-sm text-gray-700">
                          {program.careers.slice(0, 3).join(", ")}
                          {program.careers.length > 3 ? ", and more..." : ""}
                        </p>
                      </div>
                    )}

                    {program.courses && (
                      <div className="mt-4 mb-4">
                        <h4 className="font-medium text-aul-navy mb-2">Key Courses:</h4>
                        <p className="text-sm text-gray-700">
                          {program.courses.slice(0, 3).join(", ")}
                          {program.courses.length > 3 ? ", and more..." : ""}
                        </p>
                      </div>
                    )}

                    <Button asChild variant="outline" size="sm" className="mt-4">
                      <Link to={`/programs/${program.slug}`} className="flex items-center">
                        Explore Program <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>

          <section className="mt-12">
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl font-bold text-aul-navy">Research & Creative Activities</CardTitle>
                <CardDescription>Advancing knowledge and creative expression in arts and humanities</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 mb-4">
                  The Faculty of Arts and Humanities at AUL is committed to advancing knowledge and creative expression through research and artistic production. Our faculty members and students engage in diverse scholarly and creative activities that contribute to our understanding of human culture, communication, and expression.
                </p>

                <h3 className="text-xl font-semibold text-aul-navy mb-3">Key Research Areas</h3>
                <div className="grid md:grid-cols-3 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-medium text-aul-navy mb-2">Media & Communication</h4>
                    <ul className="list-disc list-inside text-gray-700 text-sm space-y-1">
                      <li>Digital Media and Society</li>
                      <li>Media Ethics and Regulation</li>
                      <li>Film and Television Studies</li>
                      <li>Journalism and Public Discourse</li>
                      <li>Social Media Analysis</li>
                    </ul>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-medium text-aul-navy mb-2">Language & Literature</h4>
                    <ul className="list-disc list-inside text-gray-700 text-sm space-y-1">
                      <li>Literary Criticism and Theory</li>
                      <li>Comparative Literature</li>
                      <li>Linguistics and Language Studies</li>
                      <li>Creative Writing</li>
                      <li>Translation Studies</li>
                    </ul>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-medium text-aul-navy mb-2">Education & Cultural Studies</h4>
                    <ul className="list-disc list-inside text-gray-700 text-sm space-y-1">
                      <li>Educational Theory and Practice</li>
                      <li>Cultural and Identity Studies</li>
                      <li>Middle Eastern Studies</li>
                      <li>Digital Humanities</li>
                      <li>Arts in Education</li>
                    </ul>
                  </div>
                </div>

                <h3 className="text-xl font-semibold text-aul-navy mt-6 mb-3">Creative Activities</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-2">
                  <li><span className="font-medium">Student Media Productions:</span> Short films, documentaries, and multimedia projects created by students and showcased at local and international festivals.</li>
                  <li><span className="font-medium">Literary Journal:</span> Annual publication featuring creative writing, poetry, and literary criticism by students and faculty.</li>
                  <li><span className="font-medium">Media Lab:</span> Collaborative space for experimental media projects and digital storytelling initiatives.</li>
                  <li><span className="font-medium">Cultural Events:</span> Regular seminars, workshops, and cultural events exploring contemporary issues in arts and humanities.</li>
                </ul>
              </CardContent>
            </Card>
          </section>

          <section className="mt-12">
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl font-bold text-aul-navy">Contact Information</CardTitle>
                <CardDescription>Get in touch with the Faculty of Arts & Humanities</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-8">
                  <div>
                    <h3 className="text-xl font-semibold text-aul-navy mb-4">Faculty Administration</h3>
                    <div className="space-y-3">
                      <div className="flex items-start gap-3">
                        <User className="h-5 w-5 text-aul-gold mt-0.5" />
                        <div>
                          <p className="font-medium">Faculty Dean</p>
                          <p className="text-gray-700">Dr. Emma Rodriguez</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <Mail className="h-5 w-5 text-aul-gold mt-0.5" />
                        <div>
                          <p className="font-medium">Email</p>
                          <p className="text-gray-700"><EMAIL></p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <Phone className="h-5 w-5 text-aul-gold mt-0.5" />
                        <div>
                          <p className="font-medium">Phone</p>
                          <p className="text-gray-700">(*************</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <MapPin className="h-5 w-5 text-aul-gold mt-0.5" />
                        <div>
                          <p className="font-medium">Location</p>
                          <p className="text-gray-700">Arts Center, South Campus</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-xl font-semibold text-aul-navy mb-4">Department Contacts</h3>
                    <div className="space-y-4">
                      <div>
                        <p className="font-medium text-aul-navy">Communication Arts</p>
                        <p className="text-gray-700">Dr. Michael Thompson</p>
                        <p className="text-gray-700 text-sm"><EMAIL> | (555) 456-7891</p>
                      </div>
                      <div>
                        <p className="font-medium text-aul-navy">English Language & Literature</p>
                        <p className="text-gray-700">Dr. Sarah Williams</p>
                        <p className="text-gray-700 text-sm"><EMAIL> | (555) 456-7892</p>
                      </div>
                      <div>
                        <p className="font-medium text-aul-navy">Education</p>
                        <p className="text-gray-700">Dr. David Chen</p>
                        <p className="text-gray-700 text-sm"><EMAIL> | (555) 456-7893</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-8 pt-6 border-t border-gray-200">
                  <h3 className="text-xl font-semibold text-aul-navy mb-4">Office Hours</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <p className="font-medium">Monday - Thursday</p>
                      <p className="text-gray-700">8:30 AM - 4:30 PM</p>
                    </div>
                    <div>
                      <p className="font-medium">Friday</p>
                      <p className="text-gray-700">8:30 AM - 3:00 PM</p>
                    </div>
                    <div>
                      <p className="font-medium">Saturday</p>
                      <p className="text-gray-700">9:00 AM - 1:00 PM</p>
                    </div>
                    <div>
                      <p className="font-medium">Sunday</p>
                      <p className="text-gray-700">Closed</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </section>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Arts;
