
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { ArrowRight, Book, User, Mail, Phone, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselPrevious,
  CarouselNext,
} from "@/components/ui/carousel";

const engineeringPrograms = [
  {
    name: "Mechanical Engineering",
    description: "A comprehensive program focusing on the design, analysis, and manufacturing of mechanical systems. Students develop expertise in thermodynamics, fluid mechanics, materials science, and mechanical design to solve complex engineering challenges.",
    slug: "mechanical-engineering",
    images: [
      {
        src: "/images/engineering/mechanics 1.JPG",
        alt: "AUL Mechanical Engineering Department"
      },
      {
        src: "/images/engineering/IMG_3800.JPG",
        alt: "Mechanical Engineering Lab"
      },
      {
        src: "/images/engineering/IMG_4188.JPG",
        alt: "Mechanical Workshop"
      }
    ],
    highlights: [
      "State-of-the-art mechanical engineering laboratories",
      "Hands-on design and manufacturing experience",
      "Industry-relevant curriculum with practical applications",
      "Focus on sustainable engineering solutions",
      "Preparation for professional engineering certification"
    ],
    careers: [
      "Mechanical Engineer",
      "Design Engineer",
      "Manufacturing Engineer",
      "Project Engineer",
      "Energy Systems Engineer",
      "Automotive Engineer",
      "HVAC Engineer",
      "Product Development Engineer"
    ],
    courses: [
      "Engineering Mechanics",
      "Thermodynamics",
      "Fluid Mechanics",
      "Machine Design",
      "Materials Science",
      "Manufacturing Processes",
      "Heat Transfer",
      "Control Systems"
    ]
  },
  {
    name: "Mechatronics Engineering",
    description: "An interdisciplinary program combining mechanical engineering, electronics, computer engineering, and control systems. Students learn to design and develop advanced automated systems, robotics, and smart devices for various industrial applications.",
    slug: "mechatronics-engineering",
    images: [
      {
        src: "/images/engineering/IMG_1622.JPG",
        alt: "AUL Mechatronics Lab"
      },
      {
        src: "/images/engineering/IMG_1661.JPG",
        alt: "Electronics Workshop"
      },
      {
        src: "/images/engineering/IMG_4200.JPG",
        alt: "Mechatronics Systems"
      }
    ],
    highlights: [
      "Integrated mechanical and electronic systems design",
      "Robotics and automation laboratory facilities",
      "Industry partnerships for practical training",
      "Multidisciplinary approach to engineering problems",
      "Capstone projects with real-world applications"
    ],
    careers: [
      "Mechatronics Engineer",
      "Robotics Engineer",
      "Automation Engineer",
      "Control Systems Engineer",
      "Industrial Automation Specialist",
      "IoT Systems Developer",
      "Smart Manufacturing Engineer",
      "Embedded Systems Engineer"
    ],
    courses: [
      "Mechatronics Systems Design",
      "Robotics and Automation",
      "Microcontrollers and Embedded Systems",
      "Sensors and Actuators",
      "Control Systems Engineering",
      "CAD/CAM Technologies",
      "Industrial Automation",
      "Mechanical and Electronic Integration"
    ]
  },
  {
    name: "Computer and Communication Engineering",
    description: "A specialized program focusing on the design and development of computer systems, networks, and communication technologies. Students gain expertise in hardware design, software development, telecommunications, and network security.",
    slug: "computer-communication-engineering",
    images: [
      {
        src: "/images/engineering/DJ1A9528.JPG",
        alt: "AUL Computer Engineering Lab"
      },
      {
        src: "/images/engineering/FB_IMG_1734939713273.jpg",
        alt: "Computer Programming Lab"
      },
      {
        src: "/images/engineering/FB_IMG_1734939730296.jpg",
        alt: "Communication Systems Lab"
      }
    ],
    highlights: [
      "Advanced computer and networking laboratories",
      "Focus on both hardware and software aspects",
      "Cybersecurity and data protection training",
      "Wireless and mobile communication systems",
      "Industry-standard development environments"
    ],
    careers: [
      "Computer Engineer",
      "Network Engineer",
      "Telecommunications Engineer",
      "Systems Engineer",
      "Hardware Designer",
      "Software Developer",
      "Cybersecurity Specialist",
      "IoT Solutions Architect"
    ],
    courses: [
      "Digital Systems Design",
      "Computer Architecture",
      "Data Communication and Networking",
      "Wireless Communication Systems",
      "Signal Processing",
      "Embedded Systems",
      "Network Security",
      "Mobile Application Development"
    ]
  },
  {
    name: "Master in Computer and Communication Engineering",
    description: "An advanced graduate program that builds on undergraduate knowledge to develop specialized expertise in computer systems, networks, and communication technologies. The program emphasizes research, innovation, and advanced technical skills.",
    slug: "master-computer-communication-engineering",
    images: [
      {
        src: "/images/engineering/FB_IMG_1734939973149.jpg",
        alt: "AUL Advanced Computing Lab"
      },
      {
        src: "/images/engineering/20140401_142952.jpg",
        alt: "Graduate Research Facility"
      },
      {
        src: "/images/engineering/DJ1A9528.JPG",
        alt: "Advanced Technology Research"
      }
    ],
    highlights: [
      "Advanced research opportunities in specialized areas",
      "Industry collaboration on cutting-edge projects",
      "Thesis and non-thesis options available",
      "Preparation for doctoral studies or leadership roles",
      "Focus on emerging technologies and innovation"
    ],
    careers: [
      "Senior Computer Engineer",
      "Research and Development Specialist",
      "Systems Architect",
      "Technical Project Manager",
      "Network Security Consultant",
      "Advanced Systems Designer",
      "University Lecturer",
      "Technology Consultant"
    ],
    courses: [
      "Advanced Computer Architecture",
      "Advanced Networking Technologies",
      "Wireless and Mobile Communications",
      "Advanced Signal Processing",
      "Information Security and Cryptography",
      "Cloud Computing and Distributed Systems",
      "Research Methodology",
      "Advanced Software Engineering"
    ]
  }
];

const facilitiesImages = [
  {
    src: "/images/engineering/DJ1A9528.JPG",
    alt: "AUL Engineering Faculty Building"
  },
  {
    src: "/images/engineering/mechanics 1.JPG",
    alt: "Mechanical Engineering Department"
  },
  {
    src: "/images/engineering/IMG_1622.JPG",
    alt: "Mechatronics Lab"
  },
  {
    src: "/images/engineering/FB_IMG_1734939713273.jpg",
    alt: "Computer Engineering Lab"
  },
  {
    src: "/images/engineering/IMG_3800.JPG",
    alt: "Engineering Workshop"
  },
  {
    src: "/images/engineering/IMG_4188.JPG",
    alt: "Mechanical Lab"
  },
  {
    src: "/images/engineering/IMG_1661.JPG",
    alt: "Electronics Lab"
  },
  {
    src: "/images/engineering/FB_IMG_1734939730296.jpg",
    alt: "Communication Systems"
  },
  {
    src: "/images/engineering/IMG_4200.JPG",
    alt: "Advanced Engineering Lab"
  },
  {
    src: "/images/engineering/FB_IMG_1734939973149.jpg",
    alt: "Research Facilities"
  },
  {
    src: "/images/engineering/20140401_142952.jpg",
    alt: "Graduate Research Lab"
  }
];

const Engineering = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow py-12">
        <div className="container mx-auto px-4">
          <div className="flex items-center gap-4 mb-8">
            <Book className="w-12 h-12 text-aul-gold" />
            <h1 className="text-4xl font-bold text-aul-navy">Faculty of Engineering</h1>
          </div>

          <section className="mb-12">
            <Card className="overflow-hidden">
              <Carousel className="w-full" opts={{ loop: true, duration: 20 }}>
                <CarouselContent>
                  {facilitiesImages.map((image, index) => (
                    <CarouselItem key={index}>
                      <div className="relative aspect-[21/9]">
                        <img
                          src={image.src}
                          alt={image.alt}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </CarouselItem>
                  ))}
                </CarouselContent>
                <CarouselPrevious className="left-2 z-20 bg-white text-black hover:bg-gray-200" />
                <CarouselNext className="right-2 z-20 bg-white text-black hover:bg-gray-200" />
              </Carousel>

              <CardContent className="py-6">
                <h2 className="text-2xl font-bold text-aul-navy mb-4">About the Faculty of Engineering</h2>
                <p className="text-gray-700 mb-4">
                  The Faculty of Engineering at AUL is committed to advancing technological solutions for tomorrow's challenges. Our programs in Mechanical Engineering, Mechatronics Engineering, and Computer and Communication Engineering combine rigorous theoretical foundations with hands-on practical experience, preparing students for successful careers in engineering and technology.
                </p>
                <p className="text-gray-700 mb-4">
                  Our engineering curriculum is designed to meet international standards while addressing the specific needs of the Lebanese and regional job markets. Students benefit from a balanced approach that emphasizes both fundamental engineering principles and specialized technical skills relevant to their chosen field.
                </p>
                <p className="text-gray-700">
                  With state-of-the-art laboratories, innovative research projects, and strong industry partnerships, we provide our students with the knowledge, skills, and experiences needed to solve complex engineering problems and make meaningful contributions to society. Our graduates are known for their technical expertise, problem-solving abilities, and innovative thinking.
                </p>
              </CardContent>
            </Card>
          </section>

          <section>
            <h2 className="text-2xl font-bold text-aul-navy mb-6">Our Programs</h2>
            <div className="grid md:grid-cols-2 gap-8">
              {engineeringPrograms.map((program, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow overflow-hidden">
                  <Carousel className="w-full" opts={{ loop: true, duration: 20 }}>
                    <CarouselContent>
                      {program.images.map((image, imageIndex) => (
                        <CarouselItem key={imageIndex}>
                          <div className="relative aspect-video">
                            <img
                              src={image.src}
                              alt={image.alt}
                              className="w-full h-full object-cover"
                            />
                            <div className="absolute inset-0 bg-black/20"></div>
                          </div>
                        </CarouselItem>
                      ))}
                    </CarouselContent>
                    <CarouselPrevious className="left-2 z-20 bg-white text-black hover:bg-gray-200" />
                    <CarouselNext className="right-2 z-20 bg-white text-black hover:bg-gray-200" />
                  </Carousel>

                  <CardHeader>
                    <CardTitle>{program.name}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 mb-4">{program.description}</p>

                    {program.highlights && (
                      <div className="mt-4 mb-4">
                        <h4 className="font-medium text-aul-navy mb-2">Program Highlights:</h4>
                        <ul className="list-disc pl-5 space-y-1 text-sm">
                          {program.highlights.slice(0, 3).map((highlight, idx) => (
                            <li key={idx} className="text-gray-700">{highlight}</li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {program.careers && (
                      <div className="mt-4 mb-4">
                        <h4 className="font-medium text-aul-navy mb-2">Career Opportunities:</h4>
                        <p className="text-sm text-gray-700">
                          {program.careers.slice(0, 3).join(", ")}
                          {program.careers.length > 3 ? ", and more..." : ""}
                        </p>
                      </div>
                    )}

                    {program.courses && (
                      <div className="mt-4 mb-4">
                        <h4 className="font-medium text-aul-navy mb-2">Key Courses:</h4>
                        <p className="text-sm text-gray-700">
                          {program.courses.slice(0, 3).join(", ")}
                          {program.courses.length > 3 ? ", and more..." : ""}
                        </p>
                      </div>
                    )}

                    <Button asChild variant="outline" size="sm" className="mt-4">
                      <Link to={`/programs/${program.slug}`} className="flex items-center">
                        Explore Program <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>

          <section className="mt-12">
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl font-bold text-aul-navy">Research & Innovation</CardTitle>
                <CardDescription>Advancing engineering knowledge through applied research</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 mb-4">
                  The Faculty of Engineering at AUL is actively engaged in research that addresses real-world engineering challenges and contributes to technological advancement. Our faculty members and students collaborate on innovative projects across various engineering disciplines, with a focus on practical applications and industry relevance.
                </p>

                <h3 className="text-xl font-semibold text-aul-navy mb-3">Key Research Areas</h3>
                <div className="grid md:grid-cols-3 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-medium text-aul-navy mb-2">Mechanical & Mechatronics</h4>
                    <ul className="list-disc list-inside text-gray-700 text-sm space-y-1">
                      <li>Advanced Manufacturing Technologies</li>
                      <li>Robotics and Automation</li>
                      <li>Renewable Energy Systems</li>
                      <li>Mechanical Design Optimization</li>
                      <li>Thermal and Fluid Systems</li>
                    </ul>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-medium text-aul-navy mb-2">Computer & Communication</h4>
                    <ul className="list-disc list-inside text-gray-700 text-sm space-y-1">
                      <li>Wireless Communication Networks</li>
                      <li>Cybersecurity and Data Protection</li>
                      <li>Internet of Things (IoT)</li>
                      <li>Artificial Intelligence Applications</li>
                      <li>Cloud Computing and Big Data</li>
                    </ul>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-medium text-aul-navy mb-2">Interdisciplinary Research</h4>
                    <ul className="list-disc list-inside text-gray-700 text-sm space-y-1">
                      <li>Smart Cities and Infrastructure</li>
                      <li>Sustainable Engineering Solutions</li>
                      <li>Industry 4.0 Technologies</li>
                      <li>Biomedical Engineering Applications</li>
                      <li>Energy Efficiency and Management</li>
                    </ul>
                  </div>
                </div>

                <h3 className="text-xl font-semibold text-aul-navy mt-6 mb-3">Research Facilities</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-2">
                  <li><span className="font-medium">Mechanical Engineering Laboratory:</span> Equipped with testing equipment for materials, thermodynamics, and fluid mechanics experiments.</li>
                  <li><span className="font-medium">Mechatronics and Robotics Lab:</span> Features robotics platforms, automation systems, and control engineering equipment.</li>
                  <li><span className="font-medium">Computer and Communication Lab:</span> Advanced networking equipment, hardware design tools, and software development environments.</li>
                  <li><span className="font-medium">Engineering Design Studio:</span> Collaborative space with CAD/CAM software and 3D printing capabilities for prototyping.</li>
                </ul>
              </CardContent>
            </Card>
          </section>

          <section className="mt-12">
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl font-bold text-aul-navy">Contact Information</CardTitle>
                <CardDescription>Get in touch with the Faculty of Engineering</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-8">
                  <div>
                    <h3 className="text-xl font-semibold text-aul-navy mb-4">Faculty Administration</h3>
                    <div className="space-y-3">
                      <div className="flex items-start gap-3">
                        <User className="h-5 w-5 text-aul-gold mt-0.5" />
                        <div>
                          <p className="font-medium">Faculty Dean</p>
                          <p className="text-gray-700">Dr. James Wilson</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <Mail className="h-5 w-5 text-aul-gold mt-0.5" />
                        <div>
                          <p className="font-medium">Email</p>
                          <p className="text-gray-700"><EMAIL></p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <Phone className="h-5 w-5 text-aul-gold mt-0.5" />
                        <div>
                          <p className="font-medium">Phone</p>
                          <p className="text-gray-700">(*************</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <MapPin className="h-5 w-5 text-aul-gold mt-0.5" />
                        <div>
                          <p className="font-medium">Location</p>
                          <p className="text-gray-700">Engineering Building, North Campus</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-xl font-semibold text-aul-navy mb-4">Department Contacts</h3>
                    <div className="space-y-4">
                      <div>
                        <p className="font-medium text-aul-navy">Mechanical Engineering</p>
                        <p className="text-gray-700">Dr. Michael Chen</p>
                        <p className="text-gray-700 text-sm"><EMAIL> | (555) 123-4568</p>
                      </div>
                      <div>
                        <p className="font-medium text-aul-navy">Mechatronics Engineering</p>
                        <p className="text-gray-700">Dr. Sarah Johnson</p>
                        <p className="text-gray-700 text-sm"><EMAIL> | (555) 123-4569</p>
                      </div>
                      <div>
                        <p className="font-medium text-aul-navy">Computer & Communication Engineering</p>
                        <p className="text-gray-700">Dr. Robert Lee</p>
                        <p className="text-gray-700 text-sm"><EMAIL> | (555) 123-4570</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-8 pt-6 border-t border-gray-200">
                  <h3 className="text-xl font-semibold text-aul-navy mb-4">Office Hours</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <p className="font-medium">Monday - Thursday</p>
                      <p className="text-gray-700">8:30 AM - 4:30 PM</p>
                    </div>
                    <div>
                      <p className="font-medium">Friday</p>
                      <p className="text-gray-700">8:30 AM - 3:00 PM</p>
                    </div>
                    <div>
                      <p className="font-medium">Saturday</p>
                      <p className="text-gray-700">9:00 AM - 1:00 PM</p>
                    </div>
                    <div>
                      <p className="font-medium">Sunday</p>
                      <p className="text-gray-700">Closed</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </section>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Engineering;
