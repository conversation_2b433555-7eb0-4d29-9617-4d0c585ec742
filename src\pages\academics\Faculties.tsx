import React from 'react';
import { Link } from 'react-router-dom';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Book, ArrowRight } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselPrevious,
  CarouselNext,
} from "@/components/ui/carousel";

const faculties = [
  {
    name: "Faculty of Sciences",
    departments: ["Computer Science", "Biology", "Mathematics", "Chemistry"],
    description: "Leading research and innovation in fundamental and applied sciences.",
    images: [
      {
        src: "https://images.unsplash.com/photo-1605810230434-7631ac76ec81",
        alt: "Computer Science Lab"
      },
      {
        src: "https://images.unsplash.com/photo-1562774053-701939374585",
        alt: "Biology Research"
      },
      {
        src: "https://images.unsplash.com/photo-1532094349884-543bc11b234d",
        alt: "Chemistry Lab"
      }
    ],
    path: "/faculties/sciences"
  },
  {
    name: "Faculty of Engineering",
    departments: ["Civil Engineering", "Electrical Engineering", "Mechanical Engineering", "Computer Engineering"],
    description: "Advancing technological solutions for tomorrow's challenges.",
    images: [
      {
        src: "https://images.unsplash.com/photo-*************-eab62e97a780",
        alt: "Engineering Workshop"
      },
      {
        src: "https://images.unsplash.com/photo-*************-47ba0277781c",
        alt: "Computer Engineering Lab"
      },
      {
        src: "https://images.unsplash.com/photo-*************-d4dc5ebe6122",
        alt: "Civil Engineering Project"
      }
    ],
    path: "/faculties/engineering"
  },
  {
    name: "Faculty of Business",
    departments: ["Business Administration", "Finance", "Marketing", "Accounting"],
    description: "Developing future business leaders and entrepreneurs.",
    images: [
      {
        src: "https://images.unsplash.com/photo-**********-d307ca884978",
        alt: "Business Presentation"
      },
      {
        src: "https://images.unsplash.com/photo-**********-8e7e53415bb0",
        alt: "Finance Lab"
      },
      {
        src: "https://images.unsplash.com/photo-*************-935f663eb1f4",
        alt: "Marketing Class"
      }
    ],
    path: "/faculties/business"
  },
  {
    name: "Faculty of Arts",
    departments: ["Graphic Design", "Digital Media", "English Literature", "Psychology"],
    description: "Fostering creativity, critical thinking, and cultural understanding.",
    images: [
      {
        src: "https://images.unsplash.com/photo-*************-4245e9b90334",
        alt: "Digital Media Lab"
      },
      {
        src: "https://images.unsplash.com/photo-*************-1fb2b075b655",
        alt: "Art Studio"
      },
      {
        src: "https://images.unsplash.com/photo-1524995997946-a1c2e315a42f",
        alt: "Psychology Research"
      }
    ],
    path: "/faculties/arts"
  }
];

const Faculties = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow py-12">
        <div className="container mx-auto px-4">
          <div className="flex items-center gap-4 mb-8">
            <Book className="w-12 h-12 text-aul-gold" />
            <h1 className="text-4xl font-bold text-aul-navy">Our Faculties</h1>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {faculties.map((faculty, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow overflow-hidden">
                <Carousel className="w-full" opts={{ loop: true, duration: 20 }}>
                  <CarouselContent>
                    {faculty.images.map((image, imageIndex) => (
                      <CarouselItem key={imageIndex}>
                        <div className="relative aspect-video">
                          <img 
                            src={image.src} 
                            alt={image.alt} 
                            className="w-full h-full object-cover"
                          />
                          <div className="absolute inset-0 bg-black/20"></div>
                        </div>
                      </CarouselItem>
                    ))}
                  </CarouselContent>
                  <CarouselPrevious className="left-2 z-20 bg-white text-black hover:bg-gray-200" />
                  <CarouselNext className="right-2 z-20 bg-white text-black hover:bg-gray-200" />
                </Carousel>

                <CardHeader>
                  <CardTitle>{faculty.name}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4">{faculty.description}</p>
                  <h3 className="font-semibold mb-2">Departments:</h3>
                  <ul className="list-disc list-inside space-y-1 text-gray-600 mb-4">
                    {faculty.departments.map((dept, idx) => (
                      <li key={idx}>{dept}</li>
                    ))}
                  </ul>
                  <Button asChild variant="outline" size="sm" className="mt-2">
                    <Link to={faculty.path} className="flex items-center">
                      Explore Faculty <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Faculties;
