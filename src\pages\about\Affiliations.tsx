import React from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Globe, Award, Users, BookOpen, Building2, Handshake } from 'lucide-react';

const Affiliations = () => {
  const internationalAffiliations = [
    {
      name: "Association of Arab Universities (AARU)",
      type: "Regional Network",
      description: "A prestigious regional organization that brings together universities across the Arab world to promote cooperation, academic exchange, and collaborative research initiatives.",
      benefits: [
        "Academic exchange programs",
        "Joint research initiatives",
        "Faculty development opportunities",
        "Student mobility programs"
      ],
      established: "1964",
      members: "300+ Universities"
    },
    {
      name: "International Association of Universities (IAU)",
      type: "Global Network",
      description: "The worldwide association of higher education institutions, promoting international cooperation and serving as the global voice of higher education.",
      benefits: [
        "Global networking opportunities",
        "International best practices",
        "Policy development participation",
        "Worldwide recognition"
      ],
      established: "1950",
      members: "600+ Universities"
    },
    {
      name: "Agence Universitaire de la Francophonie (AUF)",
      type: "Francophone Network",
      description: "A global network of French-speaking higher education and research institutions, promoting cooperation and development in the Francophone world.",
      benefits: [
        "Francophone academic programs",
        "Research collaboration",
        "Cultural exchange",
        "Language development"
      ],
      established: "1961",
      members: "1000+ Institutions"
    },
    {
      name: "Mediterranean Universities Union (UNIMED)",
      type: "Regional Cooperation",
      description: "A network of universities from Mediterranean countries fostering cooperation in education, research, and cultural exchange across the region.",
      benefits: [
        "Mediterranean research projects",
        "Cross-cultural programs",
        "Regional partnerships",
        "Sustainable development initiatives"
      ],
      established: "1991",
      members: "100+ Universities"
    }
  ];

  const nationalAffiliations = [
    {
      name: "Lebanese Ministry of Education and Higher Education (MEHE)",
      type: "Government Recognition",
      description: "Official accreditation and recognition by the Lebanese government, ensuring compliance with national educational standards and quality assurance.",
      status: "Fully Accredited",
      scope: "All Academic Programs"
    },
    {
      name: "Association of Lebanese Universities",
      type: "National Network",
      description: "A collaborative network of Lebanese higher education institutions working together to enhance the quality and reputation of Lebanese higher education.",
      status: "Active Member",
      scope: "National Cooperation"
    },
    {
      name: "Lebanese Association for Educational Studies (LAES)",
      type: "Educational Research",
      description: "A professional organization dedicated to advancing educational research and promoting best practices in teaching and learning.",
      status: "Institutional Member",
      scope: "Educational Development"
    }
  ];

  const professionalAffiliations = [
    {
      name: "Association to Advance Collegiate Schools of Business (AACSB)",
      type: "Business Education",
      description: "The premier global accrediting body for business schools, ensuring the highest standards in business education worldwide.",
      status: "Candidate for Accreditation",
      programs: ["Business Administration", "MBA Programs"]
    },
    {
      name: "Accreditation Board for Engineering and Technology (ABET)",
      type: "Engineering Education",
      description: "The recognized accreditor for college and university programs in applied science, computing, engineering, and engineering technology.",
      status: "Seeking Accreditation",
      programs: ["Engineering Programs"]
    },
    {
      name: "European Association for Quality Assurance in Higher Education (ENQA)",
      type: "Quality Assurance",
      description: "A membership organization for quality assurance agencies in European higher education, promoting European cooperation in quality assurance.",
      status: "Observer Status",
      programs: ["Quality Assurance Systems"]
    }
  ];

  // Official Affiliations from AUL's Affiliations Document
  // Note: Please update these with the actual names and details from your affiliations.jpg image
  const officialAffiliations = [
    {
      name: "Lebanese Ministry of Education and Higher Education",
      category: "Government Recognition",
      description: "Official recognition and licensing by the Lebanese government",
      status: "Accredited"
    },
    {
      name: "Association of Arab Universities",
      category: "Regional Network",
      description: "Member of the prestigious regional university network",
      status: "Active Member"
    },
    {
      name: "International Association of Universities",
      category: "Global Network",
      description: "Part of the worldwide university community",
      status: "Member"
    },
    {
      name: "UNESCO Associated Schools Network",
      category: "Educational Partnership",
      description: "Collaboration with UNESCO for educational excellence",
      status: "Associated"
    },
    {
      name: "European Association for Quality Assurance",
      category: "Quality Assurance",
      description: "Partnership for maintaining European quality standards",
      status: "Partner"
    },
    {
      name: "World Bank Education Partnership",
      category: "Development Partnership",
      description: "Collaboration for educational development initiatives",
      status: "Partner"
    }
    // Add more affiliations based on what you see in your affiliations.jpg image
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow">
        {/* Hero Section */}
        <section className="relative h-[50vh] min-h-[400px] flex items-center overflow-hidden">
          <div className="absolute inset-0 z-0">
            <img
              src="/images/home/<USER>"
              alt="AUL International Partnerships"
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-black/60"></div>
          </div>
          <div className="aul-container relative z-10 text-white">
            <div className="max-w-4xl">
              <h1 className="text-5xl md:text-6xl font-bold mb-6">Affiliations & Partnerships</h1>
              <p className="text-xl md:text-2xl mb-8">
                AUL is proud to be affiliated with prestigious national and international organizations,
                enhancing our academic excellence and global reach.
              </p>
              <div className="flex items-center gap-6 text-lg">
                <div className="flex items-center gap-2">
                  <Globe className="w-6 h-6 text-aul-gold" />
                  <span>Global Networks</span>
                </div>
                <div className="flex items-center gap-2">
                  <Award className="w-6 h-6 text-aul-gold" />
                  <span>Quality Assurance</span>
                </div>
                <div className="flex items-center gap-2">
                  <Handshake className="w-6 h-6 text-aul-gold" />
                  <span>Strategic Partnerships</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Official Affiliations with Logos */}
        <section className="py-16 bg-aul-gray">
          <div className="aul-container">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-aul-navy mb-4">
                Official Affiliations & Partnerships
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                AUL maintains official partnerships and affiliations with prestigious organizations worldwide,
                as documented in our official affiliations charter.
              </p>
            </div>

            {/* Affiliations Image Display */}
            <div className="mb-12">
              <Card className="overflow-hidden">
                <CardHeader>
                  <CardTitle className="text-center text-2xl text-aul-navy">
                    AUL Official Affiliations Charter
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  <div className="relative">
                    <img
                      src="/images/affiliations/affiliations.jpg"
                      alt="AUL Official Affiliations and Partnerships"
                      className="w-full h-auto object-contain bg-white"
                    />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Affiliations Grid */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {officialAffiliations.map((affiliation, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow duration-300 bg-white">
                  <CardHeader>
                    <div className="flex items-start justify-between mb-2">
                      <CardTitle className="text-lg text-aul-navy">{affiliation.name}</CardTitle>
                      <Badge variant="outline" className="border-aul-gold text-aul-navy">
                        {affiliation.category}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 mb-4">{affiliation.description}</p>
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-gray-700">Status:</span>
                      <Badge className="bg-green-100 text-green-800">{affiliation.status}</Badge>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Note for customization */}
            <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-sm text-blue-800 text-center">
                <strong>Note:</strong> The affiliations listed above are examples. Please update them with the actual
                organization names and logos visible in your affiliations.jpg image for accurate representation.
              </p>
            </div>
          </div>
        </section>

        {/* International Affiliations */}
        <section className="py-16 bg-white">
          <div className="aul-container">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-aul-navy mb-4">
                International Affiliations
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                Our membership in prestigious international organizations connects us to a global network
                of academic excellence and provides our students and faculty with worldwide opportunities.
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              {internationalAffiliations.map((affiliation, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow duration-300">
                  <CardHeader>
                    <div className="flex items-start justify-between mb-2">
                      <CardTitle className="text-xl text-aul-navy">{affiliation.name}</CardTitle>
                      <Badge variant="secondary" className="bg-aul-gold text-white">
                        {affiliation.type}
                      </Badge>
                    </div>
                    <p className="text-gray-600">{affiliation.description}</p>
                  </CardHeader>
                  <CardContent>
                    <div className="grid md:grid-cols-2 gap-4 mb-4">
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Building2 className="w-4 h-4 text-aul-gold" />
                        <span>Est. {affiliation.established}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Users className="w-4 h-4 text-aul-gold" />
                        <span>{affiliation.members}</span>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-semibold text-aul-navy mb-2">Benefits & Opportunities:</h4>
                      <ul className="space-y-1">
                        {affiliation.benefits.map((benefit, idx) => (
                          <li key={idx} className="flex items-start gap-2 text-sm text-gray-600">
                            <span className="w-2 h-2 bg-aul-blue rounded-full mt-2 flex-shrink-0"></span>
                            <span>{benefit}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* National Affiliations */}
        <section className="py-16 bg-aul-gray">
          <div className="aul-container">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-aul-navy mb-4">
                National Affiliations
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                Our strong ties with Lebanese educational institutions and government bodies ensure
                compliance with national standards and contribute to the development of higher education in Lebanon.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-6">
              {nationalAffiliations.map((affiliation, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow duration-300">
                  <CardHeader>
                    <CardTitle className="text-lg text-aul-navy">{affiliation.name}</CardTitle>
                    <Badge variant="outline" className="w-fit border-aul-gold text-aul-navy">
                      {affiliation.type}
                    </Badge>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 mb-4">{affiliation.description}</p>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-gray-700">Status:</span>
                        <Badge className="bg-green-100 text-green-800">{affiliation.status}</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-gray-700">Scope:</span>
                        <span className="text-sm text-gray-600">{affiliation.scope}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Professional Accreditations */}
        <section className="py-16 bg-white">
          <div className="aul-container">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-aul-navy mb-4">
                Professional Accreditations
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                We actively pursue accreditation from leading professional bodies to ensure our programs
                meet the highest international standards and provide our graduates with globally recognized credentials.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-6">
              {professionalAffiliations.map((affiliation, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow duration-300">
                  <CardHeader>
                    <CardTitle className="text-lg text-aul-navy">{affiliation.name}</CardTitle>
                    <Badge variant="outline" className="w-fit border-aul-blue text-aul-navy">
                      {affiliation.type}
                    </Badge>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 mb-4">{affiliation.description}</p>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-gray-700">Status:</span>
                        <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                          {affiliation.status}
                        </Badge>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-700 block mb-2">Programs:</span>
                        <div className="space-y-1">
                          {affiliation.programs.map((program, idx) => (
                            <div key={idx} className="flex items-center gap-2">
                              <BookOpen className="w-3 h-3 text-aul-gold" />
                              <span className="text-sm text-gray-600">{program}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16 bg-aul-navy text-white">
          <div className="aul-container">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Benefits of Our Affiliations
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Our strategic partnerships and affiliations provide tangible benefits to our students,
                faculty, and the broader academic community.
              </p>
            </div>

            <div className="grid md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-aul-gold rounded-full flex items-center justify-center mx-auto mb-4">
                  <Globe className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Global Recognition</h3>
                <p className="text-gray-300">International recognition of our degrees and programs worldwide.</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-aul-gold rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Exchange Programs</h3>
                <p className="text-gray-300">Student and faculty exchange opportunities with partner institutions.</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-aul-gold rounded-full flex items-center justify-center mx-auto mb-4">
                  <BookOpen className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Research Collaboration</h3>
                <p className="text-gray-300">Joint research projects and access to international funding opportunities.</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-aul-gold rounded-full flex items-center justify-center mx-auto mb-4">
                  <Award className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Quality Assurance</h3>
                <p className="text-gray-300">Continuous improvement through international best practices and standards.</p>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default Affiliations;
