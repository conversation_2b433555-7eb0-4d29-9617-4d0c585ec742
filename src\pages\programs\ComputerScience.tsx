
import React from 'react';
import ProgramDetail from '@/components/programs/ProgramDetail';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

const ComputerScience = () => {
  const programData = {
    name: "Bachelor of Science in Computer Science",
    description: "The Computer Science program at AUL provides students with a comprehensive foundation in computing theory, software development, and cutting-edge technologies. Our curriculum combines theoretical knowledge with practical applications, preparing graduates for successful careers in the rapidly evolving technology sector. Students gain hands-on experience through practical projects, industry collaborations, and state-of-the-art laboratories.",
    duration: "3 years (6 semesters)",
    faculty: "Faculty of Sciences",
    degreeAwarded: "Bachelor of Science (BSc) in Computer Science",
    majorHighlights: [
      "Comprehensive curriculum covering both theoretical foundations and practical applications",
      "State-of-the-art computer laboratories with the latest hardware and software",
      "Experienced faculty members with industry and academic backgrounds",
      "Opportunities for internships with leading technology companies",
      "Focus on emerging technologies including AI, machine learning, and cloud computing",
      "Capstone projects that solve real-world problems",
      "Small class sizes ensuring personalized attention",
      "Preparation for industry-recognized certifications"
    ],
    careerOpportunities: [
      "Software Developer/Engineer",
      "Web Developer",
      "Mobile Application Developer",
      "Database Administrator",
      "Systems Analyst",
      "Network Administrator",
      "IT Project Manager",
      "Data Scientist",
      "Artificial Intelligence Engineer",
      "Cloud Solutions Architect",
      "DevOps Engineer",
      "Cybersecurity Specialist",
      "Machine Learning Engineer",
      "Full-Stack Developer",
      "Research and Development"
    ],
    courseHighlights: [
      {
        year: "First Year - Semester 1",
        courses: [
          "CSCI 201: Introduction to Programming (3 credits)",
          "CSCI 203: Introduction to Computing (3 credits)",
          "MATH 211: Calculus I (3 credits)",
          "ENGL 201: English Communication Skills I (3 credits)",
          "ARAB 201: Arabic Essay Reading and Writing I (2 credits)"
        ]
      },
      {
        year: "First Year - Semester 2",
        courses: [
          "CSCI 202: Object Oriented Programming (3 credits)",
          "CSCI 204: Digital Logic Design (3 credits)",
          "MATH 212: Calculus II (3 credits)",
          "MATH 213: Linear Algebra (3 credits)",
          "ENGL 202: English Communication Skills II (3 credits)"
        ]
      },
      {
        year: "Second Year - Semester 3",
        courses: [
          "CSCI 301: Data Structures and Algorithms (3 credits)",
          "CSCI 303: Computer Organization and Assembly Language (3 credits)",
          "CSCI 305: Database Systems (3 credits)",
          "MATH 311: Discrete Mathematics (3 credits)",
          "BLAW 301: Business Law (2 credits)"
        ]
      },
      {
        year: "Second Year - Semester 4",
        courses: [
          "CSCI 302: Design and Analysis of Algorithms (3 credits)",
          "CSCI 304: Operating Systems (3 credits)",
          "CSCI 306: Computer Networks (3 credits)",
          "CSCI 308: Web Programming (3 credits)",
          "MATH 312: Numerical Analysis (3 credits)"
        ]
      },
      {
        year: "Third Year - Semester 5",
        courses: [
          "CSCI 401: Software Engineering (3 credits)",
          "CSCI 403: Artificial Intelligence (3 credits)",
          "CSCI 405: Computer Graphics (3 credits)",
          "CSCI 407: Mobile Application Development (3 credits)",
          "CSCI 409: Information Security (3 credits)"
        ]
      },
      {
        year: "Third Year - Semester 6",
        courses: [
          "CSCI 402: Distributed Systems (3 credits)",
          "CSCI 404: Machine Learning (3 credits)",
          "CSCI 406: Cloud Computing (3 credits)",
          "CSCI 408: Big Data Analytics (3 credits)",
          "CSCI 410: Senior Project (3 credits)"
        ]
      }
    ],
    admissionRequirements: [
      "Lebanese Baccalaureate or equivalent",
      "Strong foundation in Mathematics and Sciences",
      "Minimum overall average of 12/20 or equivalent",
      "English language proficiency (TOEFL score of 500+ or equivalent)",
      "Personal interview with the department",
      "Programming experience (preferred but not required)"
    ],
    programObjectives: [
      "Develop strong problem-solving and analytical skills",
      "Master fundamental programming concepts and multiple programming languages",
      "Understand computer systems, networks, and architectures",
      "Learn to design and implement efficient software solutions",
      "Gain expertise in modern development tools and methodologies",
      "Develop skills in emerging technologies like AI and cloud computing",
      "Prepare students for successful careers in the technology industry",
      "Foster innovation and entrepreneurial thinking"
    ],
    videoUrl: "https://www.youtube.com/embed/5zgjcoJ3mhA"
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow">
        <ProgramDetail program={programData} />
      </main>
      <Footer />
    </div>
  );
};

export default ComputerScience;
