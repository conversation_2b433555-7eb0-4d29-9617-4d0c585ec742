
import React from 'react';
import ProgramDetail from '@/components/programs/ProgramDetail';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

const Mathematics = () => {
  const programData = {
    name: "Bachelor of Science in Mathematics",
    description: "Our Mathematics program develops strong analytical and problem-solving skills through the study of pure and applied mathematics. Students learn to formulate, analyze, and solve abstract mathematical problems.",
    duration: "3 years",
    faculty: "Faculty of Sciences",
    degreeAwarded: "Bachelor of Science (BSc) in Mathematics",
    careerOpportunities: [
      "Data Scientist",
      "Financial Analyst",
      "Actuary",
      "Statistician",
      "Cryptographer",
      "Operations Research Analyst",
      "Mathematics Teacher/Professor",
      "Software Developer"
    ],
    courseHighlights: [
      {
        year: "First Year",
        courses: [
          "Calculus I & II",
          "Linear Algebra",
          "Discrete Mathematics",
          "Mathematical Reasoning",
          "Introduction to Computer Programming"
        ]
      },
      {
        year: "Second Year",
        courses: [
          "Multivariable Calculus",
          "Abstract Algebra",
          "Differential Equations",
          "Probability Theory",
          "Numerical Analysis"
        ]
      },
      {
        year: "Third Year",
        courses: [
          "Real Analysis",
          "Complex Analysis",
          "Mathematical Statistics",
          "Topology",
          "Senior Thesis"
        ]
      }
    ],
    admissionRequirements: [
      "High School Diploma or equivalent",
      "Minimum GPA of 3.0",
      "Strong background in Mathematics",
      "Problem-solving aptitude",
      "English language proficiency",
      "Statement of purpose"
    ],
    programObjectives: [
      "Develop strong analytical thinking and problem-solving skills",
      "Master fundamental mathematical concepts and theories",
      "Apply mathematical methods to real-world problems",
      "Communicate mathematical concepts clearly and precisely",
      "Use mathematical software and computational tools effectively",
      "Prepare for careers requiring strong quantitative abilities"
    ]
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow">
        <ProgramDetail program={programData} />
      </main>
      <Footer />
    </div>
  );
};

export default Mathematics;
