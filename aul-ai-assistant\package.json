{"name": "aul-ai-teaching-assistant", "version": "1.0.0", "description": "AI-powered teaching assistant for AUL (American University of Lebanon)", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit", "server:dev": "nodemon server/index.ts", "server:build": "tsc -p server/tsconfig.json", "server:start": "node dist/server/index.js", "setup": "npm install && npm run setup:firebase", "setup:firebase": "echo 'Please configure Firebase credentials in .env file'", "deploy:frontend": "npm run build && echo 'Deploy to your preferred hosting service'", "deploy:backend": "npm run server:build && echo 'Deploy to your preferred cloud service'"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "firebase": "^10.7.1", "axios": "^1.6.2", "uuid": "^9.0.1", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "date-fns": "^3.0.6", "clsx": "^2.0.0", "lucide-react": "^0.294.0", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-button": "^1.0.4", "@radix-ui/react-card": "^1.0.4", "@radix-ui/react-input": "^1.0.4", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-badge": "^1.0.4"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/uuid": "^9.0.7", "@types/node": "^20.10.4", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.0.8", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "nodemon": "^3.0.2", "ts-node": "^10.9.2"}, "serverDependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "multer": "^1.4.5-lts.1", "pdf-parse": "^1.1.1", "mammoth": "^1.6.0", "node-cron": "^3.0.3", "@google/generative-ai": "^0.2.1"}, "serverDevDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node-cron": "^3.0.11"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["ai", "teaching-assistant", "education", "university", "chatbot", "rag", "gemini", "firebase", "react", "typescript"], "author": "AUL Development Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/aul-edu/ai-teaching-assistant.git"}, "bugs": {"url": "https://github.com/aul-edu/ai-teaching-assistant/issues"}, "homepage": "https://ai-assistant.aul.edu.lb"}