
import React from 'react';
import ProgramDetail from '@/components/programs/ProgramDetail';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

const GraphicDesign = () => {
  const programData = {
    name: "Bachelor of Arts in Graphic Design",
    description: "The Graphic Design program at AUL combines artistic creativity with technical skills to prepare students for successful careers in visual communication. Our curriculum balances design theory with practical applications, allowing students to develop their unique creative voice while mastering industry-standard tools and techniques. Graduates emerge with the ability to create compelling designs across various media platforms and solve complex visual communication challenges.",
    duration: "3 years (6 semesters)",
    faculty: "Faculty of Arts",
    degreeAwarded: "Bachelor of Arts (BA) in Graphic Design",
    majorHighlights: [
      "Professional-grade design studios and computer labs",
      "Industry-standard software and equipment",
      "Experienced faculty of practicing designers and artists",
      "Regular workshops with visiting design professionals",
      "Opportunities to work on real client projects",
      "Annual student design exhibitions",
      "Focus on both traditional and digital design techniques",
      "Preparation for professional design certification"
    ],
    careerOpportunities: [
      "Graphic Designer",
      "Art Director",
      "Brand Identity Designer",
      "Packaging Designer",
      "UI/UX Designer",
      "Web Designer",
      "Illustrator",
      "Publication Designer",
      "Motion Graphics Designer",
      "Environmental Graphic Designer",
      "Exhibition Designer",
      "Advertising Designer",
      "Creative Director",
      "Design Consultant",
      "Design Educator"
    ],
    courseHighlights: [
      {
        year: "First Year - Semester 1",
        courses: [
          "GDES 201: Design Fundamentals (3 credits)",
          "GDES 203: Drawing Techniques (3 credits)",
          "GDES 205: Color Theory (3 credits)",
          "ENGL 201: English Communication Skills I (3 credits)",
          "ARAB 201: Arabic Essay Reading and Writing I (2 credits)"
        ]
      },
      {
        year: "First Year - Semester 2",
        courses: [
          "GDES 202: Typography Basics (3 credits)",
          "GDES 204: Digital Imaging (3 credits)",
          "GDES 206: Design History (3 credits)",
          "GDES 208: Visual Communication (3 credits)",
          "ENGL 202: English Communication Skills II (3 credits)"
        ]
      },
      {
        year: "Second Year - Semester 3",
        courses: [
          "GDES 301: Advanced Typography (3 credits)",
          "GDES 303: Branding and Identity (3 credits)",
          "GDES 305: Web Design Fundamentals (3 credits)",
          "GDES 307: Illustration Techniques (3 credits)",
          "GDES 309: Photography for Designers (2 credits)"
        ]
      },
      {
        year: "Second Year - Semester 4",
        courses: [
          "GDES 302: Package Design (3 credits)",
          "GDES 304: Editorial Design (3 credits)",
          "GDES 306: User Experience Design (3 credits)",
          "GDES 308: Motion Graphics (3 credits)",
          "GDES 310: Design Research Methods (2 credits)"
        ]
      },
      {
        year: "Third Year - Semester 5",
        courses: [
          "GDES 401: Advanced Design Studio (3 credits)",
          "GDES 403: Interactive Design (3 credits)",
          "GDES 405: Professional Practice (3 credits)",
          "GDES 407: Design Entrepreneurship (3 credits)",
          "GDES 409: Portfolio Development I (2 credits)"
        ]
      },
      {
        year: "Third Year - Semester 6",
        courses: [
          "GDES 402: Design for Social Impact (3 credits)",
          "GDES 404: Advanced Digital Applications (3 credits)",
          "GDES 406: Design Internship (2 credits)",
          "GDES 408: Portfolio Development II (3 credits)",
          "GDES 410: Capstone Project (4 credits)"
        ]
      }
    ],
    admissionRequirements: [
      "Lebanese Baccalaureate or equivalent",
      "Portfolio of creative work",
      "Minimum overall average of 12/20 or equivalent",
      "Demonstrated artistic ability",
      "English language proficiency (TOEFL score of 500+ or equivalent)",
      "Personal interview with the department"
    ],
    programObjectives: [
      "Develop strong visual communication and design skills",
      "Master industry-standard design software and techniques",
      "Apply design thinking to solve visual communication problems",
      "Understand design principles and their application across media",
      "Develop critical thinking and analytical skills for design evaluation",
      "Build a professional portfolio showcasing diverse design work",
      "Cultivate professional practices and ethical standards in design",
      "Prepare for successful careers in advertising, publishing, and digital media"
    ],
    videoUrl: "https://www.youtube.com/embed/dFSia1LZI4Y"
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-grow">
        <ProgramDetail program={programData} />
      </main>
      <Footer />
    </div>
  );
};

export default GraphicDesign;
