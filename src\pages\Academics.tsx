
import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { Link } from 'react-router-dom';
import { Book, GraduationCap, School } from 'lucide-react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

// Mock data for academic programs with links
const programs = {
  undergraduate: [
    {
      id: 1,
      name: "Bachelor of Science in Computer Science",
      description: "Comprehensive program covering software development, algorithms, and computing theory.",
      duration: "4 years",
      faculty: "Faculty of Sciences",
      link: "/programs/computer-science"
    },
    {
      id: 2,
      name: "Bachelor of Science in Biology",
      description: "Study of living organisms, molecular biology, ecology, and evolutionary biology.",
      duration: "4 years",
      faculty: "Faculty of Sciences",
      link: "/programs/biology"
    },
    {
      id: 3,
      name: "Bachelor of Science in Mathematics",
      description: "Study of abstract mathematical concepts, analysis, and applied mathematics.",
      duration: "3 years",
      faculty: "Faculty of Sciences",
      link: "/programs/mathematics"
    },
    {
      id: 4,
      name: "Bachelor of Science in Chemistry",
      description: "Study of chemical substances, processes, and transformations.",
      duration: "4 years",
      faculty: "Faculty of Sciences",
      link: "/programs/chemistry"
    },
    {
      id: 5,
      name: "Bachelor of Engineering in Civil Engineering",
      description: "Design and construction of infrastructure projects and built environment.",
      duration: "4 years",
      faculty: "Faculty of Engineering",
      link: "/programs/civil-engineering"
    },
    {
      id: 6,
      name: "Bachelor of Engineering in Electrical Engineering",
      description: "Study of electricity, electronics, and electromagnetism for various applications.",
      duration: "4 years",
      faculty: "Faculty of Engineering",
      link: "/programs/electrical-engineering"
    },
    {
      id: 7,
      name: "Bachelor of Engineering in Mechanical Engineering",
      description: "Study of mechanical systems, thermodynamics, and materials.",
      duration: "4 years",
      faculty: "Faculty of Engineering",
      link: "/programs/mechanical-engineering"
    },
    {
      id: 8,
      name: "Bachelor of Engineering in Computer Engineering",
      description: "Integration of electrical engineering and computer science principles.",
      duration: "4 years",
      faculty: "Faculty of Engineering",
      link: "/programs/computer-engineering"
    },
    {
      id: 9,
      name: "Bachelor of Business Administration",
      description: "Study of business management, finance, marketing, and entrepreneurship.",
      duration: "3 years",
      faculty: "Faculty of Business",
      link: "/programs/business-administration"
    },
    {
      id: 10,
      name: "Bachelor of Science in Finance",
      description: "Study of financial markets, investments, and corporate finance.",
      duration: "3 years",
      faculty: "Faculty of Business",
      link: "/programs/finance"
    },
    {
      id: 11,
      name: "Bachelor of Science in Marketing",
      description: "Study of consumer behavior, market research, and promotion strategies.",
      duration: "3 years",
      faculty: "Faculty of Business",
      link: "/programs/marketing"
    },
    {
      id: 12,
      name: "Bachelor of Science in Accounting",
      description: "Study of financial reporting, auditing, taxation, and management accounting.",
      duration: "3 years",
      faculty: "Faculty of Business",
      link: "/programs/accounting"
    },
    {
      id: 13,
      name: "Bachelor of Arts in Graphic Design",
      description: "Creative visual communication, typography, and digital design.",
      duration: "4 years",
      faculty: "Faculty of Arts",
      link: "/programs/graphic-design"
    },
    {
      id: 14,
      name: "Bachelor of Arts in Digital Media",
      description: "Creation and distribution of digital content across multiple platforms.",
      duration: "4 years",
      faculty: "Faculty of Arts",
      link: "/programs/digital-media"
    },
    {
      id: 15,
      name: "Bachelor of Arts in English Literature",
      description: "Study of literary texts, critical analysis, and cultural contexts.",
      duration: "3 years",
      faculty: "Faculty of Arts",
      link: "/programs/english-literature"
    },
    {
      id: 16,
      name: "Bachelor of Arts in Psychology",
      description: "Study of human behavior, mental processes, and cognitive development.",
      duration: "3 years",
      faculty: "Faculty of Arts",
      link: "/programs/psychology"
    }
  ],
  graduate: [
    {
      id: 1,
      name: "Master of Science in Data Science",
      description: "Advanced study of data analytics, machine learning, and statistical analysis.",
      duration: "2 years",
      faculty: "Faculty of Sciences",
      link: "/programs/data-science"
    },
    {
      id: 2,
      name: "Master of Business Administration (MBA)",
      description: "Advanced business studies with focus on leadership and strategic management.",
      duration: "2 years",
      faculty: "Faculty of Business",
      link: "/programs/mba"
    },
    {
      id: 3,
      name: "Master of Engineering in Sustainable Systems",
      description: "Advanced study of environmentally sustainable engineering solutions.",
      duration: "2 years",
      faculty: "Faculty of Engineering",
      link: "/programs/sustainable-systems"
    },
    {
      id: 4,
      name: "Master of Arts in Creative Media",
      description: "Advanced exploration of digital storytelling and creative technologies.",
      duration: "2 years",
      faculty: "Faculty of Arts",
      link: "/programs/creative-media"
    }
  ]
};

const academicResources = [
  {
    title: "Academic Calendar",
    description: "Important dates and deadlines for the academic year",
    icon: <Book className="h-6 w-6" />,
    link: "/academics/calendar"
  },
  {
    title: "Course Catalog",
    description: "Comprehensive list of all courses offered",
    icon: <GraduationCap className="h-6 w-6" />,
    link: "/academics/courses"
  },
  {
    title: "Academic Departments",
    description: "Browse our academic departments and faculty",
    icon: <School className="h-6 w-6" />,
    link: "/academics/departments"
  }
];

const Academics = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow">
        {/* Hero Section */}
        <section className="bg-gradient-to-b from-gray-50 to-white py-12 md:py-16">
          <div className="container mx-auto px-4">
            <h1 className="text-4xl md:text-5xl font-bold text-center text-gray-900 mb-4">
              Academic Programs
            </h1>
            <p className="text-lg text-gray-600 text-center max-w-3xl mx-auto">
              Discover our comprehensive range of undergraduate and graduate programs 
              designed to prepare you for success in your chosen field.
            </p>
          </div>
        </section>

        {/* Programs Section */}
        <section className="py-12 px-4">
          <div className="container mx-auto">
            <Tabs defaultValue="undergraduate" className="w-full">
              <TabsList className="w-full justify-start mb-8">
                <TabsTrigger value="undergraduate">Undergraduate Programs</TabsTrigger>
                <TabsTrigger value="graduate">Graduate Programs</TabsTrigger>
              </TabsList>
              
              <TabsContent value="undergraduate" className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  {programs.undergraduate.map((program) => (
                    <Link to={program.link} key={program.id}>
                      <Card className="h-full hover:shadow-lg transition-shadow">
                        <CardHeader>
                          <CardTitle>{program.name}</CardTitle>
                          <CardDescription>{program.faculty}</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="text-gray-600 mb-4">{program.description}</p>
                          <p className="text-sm text-gray-500">Duration: {program.duration}</p>
                        </CardContent>
                      </Card>
                    </Link>
                  ))}
                </div>
              </TabsContent>
              
              <TabsContent value="graduate" className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  {programs.graduate.map((program) => (
                    <Card key={program.id} className="hover:shadow-lg transition-shadow">
                      <CardHeader>
                        <CardTitle>{program.name}</CardTitle>
                        <CardDescription>{program.faculty}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <p className="text-gray-600 mb-4">{program.description}</p>
                        <p className="text-sm text-gray-500">Duration: {program.duration}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </section>

        {/* Academic Resources Section */}
        <section className="bg-gray-50 py-12 px-4">
          <div className="container mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12">Academic Resources</h2>
            <div className="grid md:grid-cols-3 gap-8">
              {academicResources.map((resource, index) => (
                <Link to={resource.link} key={index} className="block">
                  <Card className="h-full hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="mb-4">{resource.icon}</div>
                      <CardTitle>{resource.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <CardDescription>{resource.description}</CardDescription>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default Academics;
