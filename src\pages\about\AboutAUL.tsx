import React from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { ArrowRight, Award, Globe, BookOpen, Users, Building, History, Target } from 'lucide-react';

const AboutAUL = () => {
  const memberships = [
    {
      name: "Association of Arab Universities",
      logo: "https://images.unsplash.com/photo-1541339907198-e08756dedf3f?auto=format&fit=crop&q=80&w=300&h=200",
      description: "A regional organization of universities in the Arab world, promoting cooperation and exchange among member institutions."
    },
    {
      name: "International Association of Universities (IAU)",
      logo: "https://images.unsplash.com/photo-1523050854058-8df90110c9f1?auto=format&fit=crop&q=80&w=300&h=200",
      description: "A global association of higher education institutions, promoting international cooperation and exchange."
    },
    {
      name: "Association of Lebanese Universities",
      logo: "https://images.unsplash.com/photo-1519452635265-7b1fbfd1e4e0?auto=format&fit=crop&q=80&w=300&h=200",
      description: "A national organization representing universities in Lebanon, working to enhance the quality of higher education in the country."
    },
    {
      name: "Agence Universitaire de la Francophonie (AUF)",
      logo: "https://images.unsplash.com/photo-1567427017947-545c5f8d16ad?auto=format&fit=crop&q=80&w=300&h=200",
      description: "A global network of French-speaking higher-education and research institutions, promoting cooperation and development."
    }
  ];

  const affiliations = [
    {
      name: "Lebanese Ministry of Education and Higher Education",
      logo: "https://images.unsplash.com/photo-1541339907198-e08756dedf3f?auto=format&fit=crop&q=80&w=300&h=200",
      description: "AUL is officially recognized and licensed by the Lebanese Ministry of Education and Higher Education."
    },
    {
      name: "Accreditation Council for Business Schools and Programs (ACBSP)",
      logo: "https://images.unsplash.com/photo-1523050854058-8df90110c9f1?auto=format&fit=crop&q=80&w=300&h=200",
      description: "AUL's business programs are accredited by ACBSP, ensuring they meet rigorous educational standards."
    },
    {
      name: "International Network for Quality Assurance Agencies in Higher Education (INQAAHE)",
      logo: "https://images.unsplash.com/photo-1519452635265-7b1fbfd1e4e0?auto=format&fit=crop&q=80&w=300&h=200",
      description: "AUL participates in INQAAHE's global network for quality assurance in higher education."
    },
    {
      name: "European Association for Quality Assurance in Higher Education (ENQA)",
      logo: "https://images.unsplash.com/photo-1567427017947-545c5f8d16ad?auto=format&fit=crop&q=80&w=300&h=200",
      description: "AUL follows ENQA standards and guidelines for quality assurance in the European Higher Education Area."
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-4xl font-bold text-aul-navy mb-8">About AUL</h1>
          
          <div className="grid md:grid-cols-3 gap-8 mb-12">
            <Card className="bg-gradient-to-br from-aul-navy to-aul-navy-dark text-white">
              <CardContent className="pt-6">
                <BookOpen className="h-12 w-12 mb-4 text-aul-gold" />
                <h2 className="text-2xl font-bold mb-2">Excellence in Education</h2>
                <p className="text-gray-100">
                  AUL is committed to providing high-quality education that prepares students for successful careers and meaningful contributions to society.
                </p>
              </CardContent>
            </Card>
            
            <Card className="bg-gradient-to-br from-aul-navy to-aul-navy-dark text-white">
              <CardContent className="pt-6">
                <Users className="h-12 w-12 mb-4 text-aul-gold" />
                <h2 className="text-2xl font-bold mb-2">Diverse Community</h2>
                <p className="text-gray-100">
                  Our vibrant community brings together students and faculty from diverse backgrounds, creating a rich learning environment.
                </p>
              </CardContent>
            </Card>
            
            <Card className="bg-gradient-to-br from-aul-navy to-aul-navy-dark text-white">
              <CardContent className="pt-6">
                <Target className="h-12 w-12 mb-4 text-aul-gold" />
                <h2 className="text-2xl font-bold mb-2">Future-Focused</h2>
                <p className="text-gray-100">
                  AUL prepares students for the challenges of tomorrow through innovative programs and forward-thinking approaches to education.
                </p>
              </CardContent>
            </Card>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8 mb-12">
            <div>
              <h2 className="text-3xl font-bold text-aul-navy mb-4">Our Story</h2>
              <p className="text-gray-700 mb-4">
                Arts, Sciences & Technology University in Lebanon (AUL) was established with a vision to provide high-quality education that combines academic excellence with practical skills development. Since our founding, we have been committed to preparing students for successful careers and meaningful contributions to society.
              </p>
              <p className="text-gray-700 mb-4">
                AUL has grown to become a respected institution of higher learning in Lebanon and the region, known for our innovative programs, dedicated faculty, and student-centered approach to education.
              </p>
              <Button asChild variant="outline" className="mt-2">
                <Link to="/about/history" className="flex items-center">
                  Learn More About Our History <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
            
            <div className="rounded-lg overflow-hidden">
              <img 
                src="https://images.unsplash.com/photo-1498243691581-b145c3f54a5a?auto=format&fit=crop&q=80&w=600" 
                alt="AUL Campus" 
                className="w-full h-full object-cover"
              />
            </div>
          </div>
          
          <Tabs defaultValue="mission" className="mb-12">
            <TabsList className="grid grid-cols-3 mb-8">
              <TabsTrigger value="mission">Mission & Vision</TabsTrigger>
              <TabsTrigger value="values">Core Values</TabsTrigger>
              <TabsTrigger value="leadership">Leadership</TabsTrigger>
            </TabsList>
            
            <TabsContent value="mission" className="bg-gray-50 p-6 rounded-lg">
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-2xl font-bold text-aul-navy mb-4">Our Mission</h3>
                  <p className="text-gray-700 mb-4">
                    AUL University is dedicated to providing excellence in education, fostering 
                    intellectual growth, and developing future leaders through innovative teaching, 
                    research, and community engagement.
                  </p>
                  <ul className="list-disc list-inside space-y-2 text-gray-600">
                    <li>Deliver high-quality education across diverse disciplines</li>
                    <li>Foster critical thinking and creativity</li>
                    <li>Promote research and innovation</li>
                    <li>Serve our community through engagement and outreach</li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="text-2xl font-bold text-aul-navy mb-4">Our Vision</h3>
                  <p className="text-gray-700 mb-4">
                    To be a leading institution of higher education in Lebanon and the region, 
                    recognized for academic excellence, innovative research, and positive impact 
                    on society.
                  </p>
                  <ul className="list-disc list-inside space-y-2 text-gray-600">
                    <li>Become a center of academic excellence</li>
                    <li>Lead in research and innovation</li>
                    <li>Foster global partnerships</li>
                    <li>Drive positive societal change</li>
                  </ul>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="values" className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-2xl font-bold text-aul-navy mb-4">Our Core Values</h3>
              <div className="grid md:grid-cols-3 gap-4">
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <h4 className="font-bold text-aul-navy mb-2">Excellence</h4>
                  <p className="text-gray-700">We strive for excellence in all aspects of our academic and administrative operations.</p>
                </div>
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <h4 className="font-bold text-aul-navy mb-2">Integrity</h4>
                  <p className="text-gray-700">We uphold the highest standards of ethics, honesty, and transparency in all our actions.</p>
                </div>
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <h4 className="font-bold text-aul-navy mb-2">Innovation</h4>
                  <p className="text-gray-700">We embrace creativity, adaptability, and forward-thinking approaches to education and research.</p>
                </div>
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <h4 className="font-bold text-aul-navy mb-2">Diversity</h4>
                  <p className="text-gray-700">We value and respect diversity of people, ideas, and perspectives in our community.</p>
                </div>
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <h4 className="font-bold text-aul-navy mb-2">Collaboration</h4>
                  <p className="text-gray-700">We foster teamwork, partnerships, and cooperation to achieve common goals.</p>
                </div>
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <h4 className="font-bold text-aul-navy mb-2">Social Responsibility</h4>
                  <p className="text-gray-700">We are committed to making positive contributions to our community and society.</p>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="leadership" className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-2xl font-bold text-aul-navy mb-4">University Leadership</h3>
              <p className="text-gray-700 mb-4">
                AUL is led by a dedicated team of experienced administrators and academics who are committed to advancing the university's mission and vision.
              </p>
              <Button asChild variant="outline" className="mt-2">
                <Link to="/about/administration" className="flex items-center">
                  Meet Our Leadership Team <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </TabsContent>
          </Tabs>
          
          {/* Memberships Section */}
          <section className="mb-12">
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl font-bold text-aul-navy flex items-center">
                  <Award className="mr-2 h-6 w-6 text-aul-gold" /> Memberships
                </CardTitle>
                <CardDescription>Organizations and networks AUL is proud to be a member of</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-6">
                  {memberships.map((membership, index) => (
                    <div key={index} className="flex gap-4 items-start">
                      <div className="w-24 h-16 flex-shrink-0 overflow-hidden rounded-md">
                        <img 
                          src={membership.logo} 
                          alt={membership.name} 
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div>
                        <h3 className="font-bold text-aul-navy">{membership.name}</h3>
                        <p className="text-sm text-gray-600">{membership.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </section>
          
          {/* Affiliations Section */}
          <section className="mb-12">
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl font-bold text-aul-navy flex items-center">
                  <Globe className="mr-2 h-6 w-6 text-aul-gold" /> Affiliations
                </CardTitle>
                <CardDescription>Institutions and organizations AUL is affiliated with</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-6">
                  {affiliations.map((affiliation, index) => (
                    <div key={index} className="flex gap-4 items-start">
                      <div className="w-24 h-16 flex-shrink-0 overflow-hidden rounded-md">
                        <img 
                          src={affiliation.logo} 
                          alt={affiliation.name} 
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div>
                        <h3 className="font-bold text-aul-navy">{affiliation.name}</h3>
                        <p className="text-sm text-gray-600">{affiliation.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </section>
          
          <div className="grid md:grid-cols-3 gap-4">
            <Button asChild variant="outline" className="flex items-center justify-center">
              <Link to="/about/accreditations">
                <Award className="mr-2 h-4 w-4" /> Accreditations
              </Link>
            </Button>
            <Button asChild variant="outline" className="flex items-center justify-center">
              <Link to="/about/campus-locations">
                <Building className="mr-2 h-4 w-4" /> Campus Locations
              </Link>
            </Button>
            <Button asChild variant="outline" className="flex items-center justify-center">
              <Link to="/about/strategic-plan">
                <Target className="mr-2 h-4 w-4" /> Strategic Plan
              </Link>
            </Button>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default AboutAUL;
