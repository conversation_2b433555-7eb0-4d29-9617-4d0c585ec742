// AI Service for AUL Teaching Assistant
// Handles communication with Gemini API and RAG pipeline

import { 
  collection, 
  query, 
  where, 
  orderBy, 
  limit, 
  getDocs,
  addDoc,
  updateDoc,
  doc
} from 'firebase/firestore';
import { 
  db, 
  COLLECTIONS, 
  DocumentChunk, 
  Message, 
  DocumentSource,
  API_CONFIG,
  handleFirebaseError 
} from '@/config/firebase';

// Types for AI service
export interface AIResponse {
  content: string;
  sources: DocumentSource[];
  metadata: {
    model: string;
    tokens: number;
    processingTime: number;
    confidence: number;
  };
}

export interface RAGContext {
  chunks: DocumentChunk[];
  query: string;
  courseId?: string;
  maxChunks: number;
}

export class AIService {
  private geminiApiKey: string;

  constructor(apiKey: string) {
    this.geminiApiKey = apiKey;
  }

  /**
   * Main method to get AI response with RAG
   */
  async getAIResponse(
    userQuery: string, 
    courseId?: string, 
    conversationHistory: Message[] = []
  ): Promise<AIResponse> {
    const startTime = Date.now();

    try {
      // Step 1: Retrieve relevant context from knowledge base
      const ragContext = await this.retrieveRelevantContext(userQuery, courseId);

      // Step 2: Construct enhanced prompt with context
      const enhancedPrompt = this.constructRAGPrompt(
        userQuery, 
        ragContext, 
        conversationHistory
      );

      // Step 3: Call Gemini API
      const aiResponse = await this.callGeminiAPI(enhancedPrompt);

      // Step 4: Extract sources from context
      const sources = this.extractSources(ragContext.chunks);

      const processingTime = Date.now() - startTime;

      return {
        content: aiResponse.content,
        sources,
        metadata: {
          model: 'gemini-pro',
          tokens: aiResponse.tokens || 0,
          processingTime,
          confidence: this.calculateConfidence(ragContext.chunks, userQuery)
        }
      };

    } catch (error) {
      console.error('AI Service Error:', error);
      throw handleFirebaseError(error);
    }
  }

  /**
   * Retrieve relevant document chunks from Firestore
   */
  private async retrieveRelevantContext(
    query: string, 
    courseId?: string, 
    maxChunks: number = 5
  ): Promise<RAGContext> {
    try {
      let chunksQuery = collection(db, COLLECTIONS.DOCUMENT_CHUNKS);
      
      // Filter by course if specified
      if (courseId) {
        chunksQuery = query(
          chunksQuery,
          where('courseId', '==', courseId),
          orderBy('createdAt', 'desc'),
          limit(maxChunks * 2) // Get more to filter later
        ) as any;
      } else {
        chunksQuery = query(
          chunksQuery,
          orderBy('createdAt', 'desc'),
          limit(maxChunks * 2)
        ) as any;
      }

      const snapshot = await getDocs(chunksQuery);
      const allChunks = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as DocumentChunk[];

      // Simple text similarity filtering (in production, use vector similarity)
      const relevantChunks = this.filterRelevantChunks(allChunks, query, maxChunks);

      return {
        chunks: relevantChunks,
        query,
        courseId,
        maxChunks
      };

    } catch (error) {
      console.error('Error retrieving context:', error);
      return { chunks: [], query, courseId, maxChunks };
    }
  }

  /**
   * Simple text similarity filtering (placeholder for vector similarity)
   */
  private filterRelevantChunks(
    chunks: DocumentChunk[], 
    query: string, 
    maxChunks: number
  ): DocumentChunk[] {
    const queryWords = query.toLowerCase().split(' ');
    
    // Score chunks based on keyword overlap
    const scoredChunks = chunks.map(chunk => {
      const chunkWords = chunk.content.toLowerCase().split(' ');
      const overlap = queryWords.filter(word => 
        chunkWords.some(chunkWord => chunkWord.includes(word))
      ).length;
      
      return {
        chunk,
        score: overlap / queryWords.length
      };
    });

    // Sort by score and return top chunks
    return scoredChunks
      .sort((a, b) => b.score - a.score)
      .slice(0, maxChunks)
      .map(item => item.chunk);
  }

  /**
   * Construct enhanced prompt with RAG context
   */
  private constructRAGPrompt(
    userQuery: string,
    ragContext: RAGContext,
    conversationHistory: Message[]
  ): string {
    let prompt = `You are an AI Teaching Assistant for AUL (American University of Lebanon). You help students with their coursework by providing accurate, helpful answers based on course materials.

CONTEXT FROM COURSE MATERIALS:
`;

    // Add relevant chunks as context
    ragContext.chunks.forEach((chunk, index) => {
      prompt += `
[Source ${index + 1}: ${chunk.metadata.title}${chunk.pageNumber ? `, Page ${chunk.pageNumber}` : ''}]
${chunk.content}
`;
    });

    // Add conversation history for context
    if (conversationHistory.length > 0) {
      prompt += `\nRECENT CONVERSATION:\n`;
      conversationHistory.slice(-3).forEach(msg => {
        prompt += `${msg.sender === 'user' ? 'Student' : 'Assistant'}: ${msg.content}\n`;
      });
    }

    prompt += `\nSTUDENT QUESTION: ${userQuery}

INSTRUCTIONS:
1. Answer the student's question using the provided course materials
2. Be accurate and cite your sources when possible
3. If the answer isn't in the provided materials, say so clearly
4. Keep your response helpful and educational
5. Use a friendly, supportive tone appropriate for a teaching assistant

RESPONSE:`;

    return prompt;
  }

  /**
   * Call Gemini API with the enhanced prompt
   */
  private async callGeminiAPI(prompt: string): Promise<{ content: string; tokens?: number }> {
    try {
      const response = await fetch(`${API_CONFIG.GEMINI_API_URL}?key=${this.geminiApiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: prompt
            }]
          }],
          generationConfig: {
            temperature: API_CONFIG.TEMPERATURE,
            topK: API_CONFIG.TOP_K,
            topP: API_CONFIG.TOP_P,
            maxOutputTokens: API_CONFIG.MAX_TOKENS,
          }
        })
      });

      if (!response.ok) {
        throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.candidates || data.candidates.length === 0) {
        throw new Error('No response generated from Gemini API');
      }

      const content = data.candidates[0].content.parts[0].text;
      const tokens = data.usageMetadata?.totalTokenCount || 0;

      return { content, tokens };

    } catch (error) {
      console.error('Gemini API Error:', error);
      throw new Error(`Failed to get AI response: ${error.message}`);
    }
  }

  /**
   * Extract sources from document chunks
   */
  private extractSources(chunks: DocumentChunk[]): DocumentSource[] {
    return chunks.map(chunk => ({
      title: chunk.metadata.title,
      page: chunk.pageNumber,
      section: chunk.section,
      confidence: 0.8 // Placeholder confidence score
    }));
  }

  /**
   * Calculate confidence score based on context relevance
   */
  private calculateConfidence(chunks: DocumentChunk[], query: string): number {
    if (chunks.length === 0) return 0.1;
    
    // Simple confidence calculation based on number of relevant chunks
    const baseConfidence = Math.min(chunks.length / 5, 1);
    const queryLength = query.split(' ').length;
    const lengthBonus = Math.min(queryLength / 10, 0.2);
    
    return Math.min(baseConfidence + lengthBonus, 0.95);
  }

  /**
   * Save conversation message to Firestore
   */
  async saveMessage(
    conversationId: string,
    content: string,
    sender: 'user' | 'ai',
    sources?: DocumentSource[]
  ): Promise<string> {
    try {
      const messageData = {
        conversationId,
        content,
        sender,
        timestamp: new Date(),
        sources: sources || [],
        metadata: {
          model: sender === 'ai' ? 'gemini-pro' : undefined,
          tokens: sender === 'ai' ? content.length : undefined,
        }
      };

      const docRef = await addDoc(collection(db, COLLECTIONS.MESSAGES), messageData);
      return docRef.id;

    } catch (error) {
      console.error('Error saving message:', error);
      throw handleFirebaseError(error);
    }
  }

  /**
   * Create a new conversation
   */
  async createConversation(
    userId: string,
    courseId?: string,
    title?: string
  ): Promise<string> {
    try {
      const conversationData = {
        userId,
        courseId,
        title: title || 'New Conversation',
        createdAt: new Date(),
        updatedAt: new Date(),
        messageCount: 0
      };

      const docRef = await addDoc(collection(db, COLLECTIONS.CONVERSATIONS), conversationData);
      return docRef.id;

    } catch (error) {
      console.error('Error creating conversation:', error);
      throw handleFirebaseError(error);
    }
  }
}

// Export singleton instance
export const aiService = new AIService(process.env.REACT_APP_GEMINI_API_KEY || '');

// Utility function to initialize AI service with API key
export const initializeAIService = (apiKey: string) => {
  return new AIService(apiKey);
};
